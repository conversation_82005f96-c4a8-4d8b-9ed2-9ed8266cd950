import React, { useState, useRef } from 'react';
import { useContent } from '@/contexts/ContentContext.jsx';
import { useToast } from '@/components/ui/use-toast.js';
import { v4 as uuidv4 } from 'uuid';
import { Button } from '@/components/ui/button.jsx';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card.jsx';
import { Input } from '@/components/ui/input.jsx';
import { Label } from '@/components/ui/label.jsx';
import { Upload, Save, Trash2, Image, X } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

const toBase64 = file => new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = error => reject(error);
});

const AdminPartnersPage = () => {
    const { partners, updateContent, isLoading } = useContent();
    const { toast } = useToast();
    const [stagedPartners, setStagedPartners] = useState([]);
    const [isSaving, setIsSaving] = useState(false);
    const fileInputRef = useRef(null);

    const handleFileChange = (event) => {
        const files = Array.from(event.target.files);
        if (files.length === 0) return;

        const newStagedPartners = files.map(file => ({
            tempId: uuidv4(),
            file: file,
            name: file.name.replace(/\.[^/.]+$/, ""),
            preview: URL.createObjectURL(file)
        }));
        
        setStagedPartners(prev => [...prev, ...newStagedPartners]);
    };

    const handleRemoveStaged = (tempId) => {
        setStagedPartners(prev => prev.filter(p => p.tempId !== tempId));
    };

    const handleStagedNameChange = (tempId, newName) => {
        setStagedPartners(prev => prev.map(p => p.tempId === tempId ? { ...p, name: newName } : p));
    };

    const handleSaveStaged = async () => {
        if (stagedPartners.length === 0) return;
        setIsSaving(true);
        toast({ title: 'Menyimpan logo...', description: 'Mohon tunggu sebentar.' });

        try {
            const newPartners = await Promise.all(stagedPartners.map(async (staged) => {
                const logoDataUrl = await toBase64(staged.file);
                return {
                    id: uuidv4(),
                    name: staged.name,
                    logo: logoDataUrl
                };
            }));

            updateContent('partners', [...partners, ...newPartners]);
            setStagedPartners([]);
            toast({ title: 'Sukses!', description: `${newPartners.length} logo mitra baru telah disimpan.` });
        } catch (error) {
            console.error("Error saving partners:", error);
            toast({ title: 'Gagal Menyimpan', description: 'Terjadi kesalahan saat memproses gambar.', variant: 'destructive' });
        } finally {
            setIsSaving(false);
        }
    };

    const handleDeletePartner = (id) => {
        const updatedPartners = partners.filter(p => p.id !== id);
        updateContent('partners', updatedPartners);
        toast({ title: 'Mitra Dihapus', variant: 'destructive' });
    };

    return (
        <div className="space-y-6">
            <h1 className="text-3xl font-bold text-white">Manajemen Mitra</h1>
            <Card className="bg-gray-800 border-gray-700 text-white">
                <CardHeader>
                    <CardTitle>Upload Logo Mitra Baru</CardTitle>
                    <CardDescription className="text-gray-400">Pilih satu atau lebih file gambar untuk diunggah. Anda dapat mengedit nama sebelum menyimpan.</CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="flex flex-col sm:flex-row gap-4">
                        <Button onClick={() => fileInputRef.current?.click()} className="flex-shrink-0">
                            <Upload className="mr-2 h-4 w-4" /> Pilih File
                        </Button>
                        <Input
                            ref={fileInputRef}
                            type="file"
                            multiple
                            accept="image/*"
                            onChange={handleFileChange}
                            className="hidden"
                        />
                         <Button onClick={handleSaveStaged} disabled={stagedPartners.length === 0 || isSaving} className="gold-gradient text-black flex-shrink-0 font-semibold">
                            <Save className="mr-2 h-4 w-4" /> {isSaving ? 'Menyimpan...' : `Simpan ${stagedPartners.length} Mitra Baru`}
                        </Button>
                    </div>
                   
                    <AnimatePresence>
                        {stagedPartners.length > 0 && (
                             <div className="mt-6 space-y-4">
                                <h3 className="text-lg font-semibold">Logo yang akan di-upload:</h3>
                                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                                    {stagedPartners.map((staged) => (
                                        <motion.div
                                            key={staged.tempId}
                                            layout
                                            initial={{ opacity: 0, scale: 0.8 }}
                                            animate={{ opacity: 1, scale: 1 }}
                                            exit={{ opacity: 0, scale: 0.8 }}
                                            className="bg-gray-700 p-3 rounded-lg space-y-2 relative"
                                        >
                                            <Button variant="ghost" size="icon" className="absolute -top-2 -right-2 h-7 w-7 bg-red-500/80 hover:bg-red-500 text-white rounded-full" onClick={() => handleRemoveStaged(staged.tempId)}>
                                                <X className="h-4 w-4"/>
                                            </Button>
                                            <img src={staged.preview} alt={staged.name} className="h-20 w-auto mx-auto object-contain rounded-md bg-white p-1" />
                                            <Input 
                                                value={staged.name} 
                                                onChange={(e) => handleStagedNameChange(staged.tempId, e.target.value)} 
                                                className="bg-gray-600 border-gray-500"
                                            />
                                        </motion.div>
                                    ))}
                                </div>
                             </div>
                        )}
                    </AnimatePresence>
                </CardContent>
            </Card>

            <Card className="bg-gray-800 border-gray-700 text-white">
                <CardHeader>
                    <CardTitle>Daftar Mitra Saat Ini</CardTitle>
                </CardHeader>
                <CardContent>
                    {isLoading ? <p>Memuat mitra...</p> :
                     partners.length > 0 ? (
                        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                            {partners.map(partner => (
                                <div key={partner.id} className="group relative bg-gray-700 p-4 rounded-lg flex flex-col items-center justify-center text-center">
                                    <img src={partner.logo} alt={partner.name} className="h-16 w-auto object-contain mb-2" />
                                    <p className="text-sm font-medium truncate w-full">{partner.name}</p>
                                    <div className="absolute inset-0 bg-black/70 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                                        <Button variant="destructive" size="sm" onClick={() => handleDeletePartner(partner.id)}>
                                            <Trash2 className="h-4 w-4 mr-1" /> Hapus
                                        </Button>
                                    </div>
                                </div>
                            ))}
                        </div>
                     ) : <p className="text-gray-400">Belum ada mitra yang ditambahkan.</p>
                    }
                </CardContent>
            </Card>
        </div>
    );
};

export default AdminPartnersPage;
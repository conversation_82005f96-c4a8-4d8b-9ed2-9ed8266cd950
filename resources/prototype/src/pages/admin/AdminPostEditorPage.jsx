import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useToast } from '@/components/ui/use-toast.js';
import { motion } from 'framer-motion';
import { useBlog } from '@/contexts/BlogContext.jsx';
import PreviewPostDialog from '@/components/admin/PreviewPostDialog.jsx';
import PostEditorHeader from '@/components/admin/PostEditorHeader.jsx';
import PostEditorForm from '@/components/admin/PostEditorForm.jsx';

const initialPostState = {
  title: '',
  slug: '',
  content: '',
  category: '',
  tags: '',
  featured_image: '',
  status: 'Draft',
  author: '<PERSON>',
  publish_date: new Date().toISOString().slice(0, 16),
  meta: {
    title: '',
    description: '',
    robots: 'index, follow'
  },
  updatedAt: null,
};

const AdminPostEditorPage = () => {
  const { postId } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { originalPosts, addPost, updatePost, getPostById, isLoading: isBlogLoading } = useBlog();
  const isNewPost = !postId;

  const [postData, setPostData] = useState(initialPostState);
  const [isSlugManuallyEdited, setIsSlugManuallyEdited] = useState(false);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  const generateSlug = useCallback((text) => {
    if (!text) return '';
    return text.toString().toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^\w-]+/g, '')
      .replace(/--+/g, '-')
      .replace(/^-+/, '')
      .replace(/-+$/, '');
  }, []);

  useEffect(() => {
    if (isBlogLoading) return;

    if (!isNewPost) {
      const postToEdit = getPostById(postId);
      if (postToEdit) {
        setPostData({
          ...initialPostState,
          ...postToEdit,
          tags: Array.isArray(postToEdit.tags) ? postToEdit.tags.join(', ') : '',
          publish_date: postToEdit.publish_date ? new Date(postToEdit.publish_date).toISOString().slice(0, 16) : new Date().toISOString().slice(0, 16),
          meta: postToEdit.meta || initialPostState.meta,
        });
        setIsSlugManuallyEdited(true);
      } else {
        toast({ title: "Error", description: "Post tidak ditemukan.", variant: "destructive" });
        navigate('/admin/blog');
      }
    } else {
      setPostData(initialPostState);
      setIsSlugManuallyEdited(false);
    }
  }, [postId, isNewPost, navigate, toast, getPostById, isBlogLoading]);

  const handleInputChange = (field, value) => {
    setPostData(prev => ({ ...prev, [field]: value }));
  };
  
  const handleMetaChange = (field, value) => {
    setPostData(prev => ({ ...prev, meta: { ...prev.meta, [field]: value } }));
  };

  const handleTitleChange = (e) => {
    const newTitle = e.target.value;
    handleInputChange('title', newTitle);
    if (!isSlugManuallyEdited) {
      handleInputChange('slug', generateSlug(newTitle));
    }
    if (!postData.meta.title) {
      handleMetaChange('title', newTitle.slice(0, 60));
    }
  };
  
  const handleSlugChange = (e) => {
    const newSlug = generateSlug(e.target.value);
    handleInputChange('slug', newSlug);
    setIsSlugManuallyEdited(true);
  };

  const handleImageUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        handleInputChange('featured_image', reader.result);
      };
      reader.readAsDataURL(file);
      toast({ title: "Gambar Dipilih", description: file.name });
    }
  };

  const removeFeaturedImage = () => {
    handleInputChange('featured_image', '');
    toast({ title: "Gambar Dihapus", description: "Featured image telah dihapus." });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSaving(true);

    const isContentEmpty = !postData.content || postData.content.replace(/<(.|\n)*?>/g, '').trim().length === 0;

    if (!postData.title.trim() || isContentEmpty) {
      toast({ 
        title: "Judul dan Isi Artikel Wajib Diisi", 
        description: "Mohon lengkapi judul dan isi artikel sebelum menyimpan.", 
        variant: "destructive" 
      });
      setIsSaving(false);
      return;
    }

    const finalSlug = postData.slug || generateSlug(postData.title);
    if (!finalSlug) {
      toast({ 
        title: "Slug Tidak Valid", 
        description: "Slug tidak dapat dibuat. Pastikan judul tidak kosong.", 
        variant: "destructive" 
      });
      setIsSaving(false);
      return;
    }
    
    const existingPostWithSameSlug = originalPosts.find(p => p.slug === finalSlug && p.id !== postId);
    if (existingPostWithSameSlug) {
      toast({ 
        title: "Slug Sudah Digunakan", 
        description: `Slug "${finalSlug}" sudah ada. Mohon gunakan slug lain.`, 
        variant: "destructive" 
      });
      setIsSaving(false);
      return;
    }

    try {
      const finalPostData = {
        ...postData,
        id: isNewPost ? undefined : postId,
        slug: finalSlug,
        tags: postData.tags.split(',').map(tag => tag.trim()).filter(Boolean),
        publish_date: new Date(postData.publish_date).toISOString(),
      };

      if (isNewPost) {
        addPost(finalPostData);
      } else {
        updatePost(finalPostData);
      }
      
      toast({
        title: `Post ${postData.status === 'Draft' ? 'Disimpan sebagai Draft' : isNewPost ? 'Berhasil Dibuat' : 'Berhasil Diperbarui'}!`,
        description: `Post "${postData.title}" telah berhasil disimpan.`,
      });
      
      navigate('/admin/blog');
    } catch (error) {
      toast({
        title: "Gagal Menyimpan",
        description: "Terjadi kesalahan saat menyimpan post.",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  const categories = [
    "Tips dan Insight", 
    "Layanan Kami", 
    "Umrah Edukasi", 
    "Panduan Haji",
    "Edukasi Jamaah",
    "Panduan Visa",
    "Tips Akomodasi",
    "Tips Hemat",
    "Panduan Ibadah",
    "Update Regulasi",
    "Tips & Trik", 
    "Artikel Islami", 
    "Review", 
    "Berita Terkini"
  ];

  if (isBlogLoading && !isNewPost) {
    return (
      <div className="text-center text-white py-10">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
        Memuat editor post...
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      <form onSubmit={handleSubmit}>
        <PostEditorHeader
          isNewPost={isNewPost}
          postData={postData}
          isSaving={isSaving}
          onBack={() => navigate('/admin/blog')}
          onPreview={() => setIsPreviewOpen(true)}
          onSubmit={handleSubmit}
        />

        <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
          <PostEditorForm
            postData={postData}
            onInputChange={handleInputChange}
            onMetaChange={handleMetaChange}
            onTitleChange={handleTitleChange}
            onSlugChange={handleSlugChange}
            onImageUpload={handleImageUpload}
            onRemoveImage={removeFeaturedImage}
            categories={categories}
          />
        </div>
      </form>
      
      <PreviewPostDialog 
        isOpen={isPreviewOpen} 
        onClose={() => setIsPreviewOpen(false)} 
        postData={postData} 
      />
    </motion.div>
  );
};

export default AdminPostEditorPage;
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card.jsx';
import { Button } from '@/components/ui/button.jsx';
import { Input } from '@/components/ui/input.jsx';
import { Label } from '@/components/ui/label.jsx';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select.jsx';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table.jsx';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog.jsx";
import { UserPlus, Search, Edit, Trash2, ShieldCheck, ShieldAlert, Shield, KeyRound, Save } from 'lucide-react';
import { motion } from 'framer-motion';
import { useToast } from '@/components/ui/use-toast.js';

const AdminUsersPage = () => {
  const { toast } = useToast();
  const [users, setUsers] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterRole, setFilterRole] = useState('all');
  
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentUser, setCurrentUser] = useState(null); // null for new user, object for editing
  const [userName, setUserName] = useState('');
  const [userEmail, setUserEmail] = useState('');
  const [userRole, setUserRole] = useState('Author');
  const [userPassword, setUserPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  useEffect(() => {
    try {
        const storedUsers = localStorage.getItem('adminUsers');
        if (storedUsers) {
            setUsers(JSON.parse(storedUsers));
        } else {
            setUsers([]); // Should be populated by AuthContext, but have a fallback.
        }
    } catch (error) {
        console.error("Failed to parse admin users from localStorage:", error);
        setUsers([]);
    }
  }, []);

  const filteredUsers = users.filter(user => {
    const matchesSearch = 
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesRole = filterRole === 'all' || user.role === filterRole;
    return matchesSearch && matchesRole;
  }).sort((a,b) => a.name.localeCompare(b.name));


  const resetForm = () => {
    setUserName('');
    setUserEmail('');
    setUserRole('Author');
    setUserPassword('');
    setConfirmPassword('');
    setCurrentUser(null);
  };

  const handleOpenModal = (user = null) => {
    if (user) {
      setCurrentUser(user);
      setUserName(user.name);
      setUserEmail(user.email);
      setUserRole(user.role);
    } else {
      resetForm();
    }
    setIsModalOpen(true);
  };

  const handleSaveUser = () => {
    if (!userName || !userEmail) {
      toast({ title: "Data Tidak Lengkap", description: "Nama dan Email wajib diisi.", variant: "destructive" });
      return;
    }
    if (!/\S+@\S+\.\S+/.test(userEmail)) {
      toast({ title: "Email Tidak Valid", description: "Format email tidak benar.", variant: "destructive" });
      return;
    }
    if (!currentUser && !userPassword) {
      toast({ title: "Kata Sandi Wajib", description: "Untuk admin baru, kata sandi wajib diisi.", variant: "destructive" });
      return;
    }

    if (userPassword || confirmPassword) {
      if (userPassword !== confirmPassword) {
        toast({ title: "Kata Sandi Tidak Cocok", description: "Kata sandi baru dan konfirmasi tidak sama.", variant: "destructive" });
        return;
      }
      if (userPassword.length < 6 && userPassword.length > 0) {
         toast({ title: "Kata Sandi Terlalu Pendek", description: "Kata sandi minimal 6 karakter.", variant: "destructive" });
         return;
      }
    }
    
    let updatedUsers;
    if (currentUser) {
      const existingUser = users.find(u => u.id === currentUser.id);
      updatedUsers = users.map(u => u.id === currentUser.id ? { 
        ...u, 
        name: userName, 
        email: userEmail, 
        role: userRole,
        password: userPassword ? userPassword : existingUser.password
      } : u);
    } else {
      const newUser = {
        id: Date.now().toString(),
        name: userName,
        email: userEmail,
        role: userRole,
        password: userPassword,
        lastLogin: 'Belum Pernah',
        status: 'Active',
        createdAt: new Date().toISOString()
      };
      updatedUsers = [...users, newUser];
    }
    setUsers(updatedUsers);
    localStorage.setItem('adminUsers', JSON.stringify(updatedUsers));
    toast({ title: `Admin ${currentUser ? 'Diperbarui' : 'Ditambahkan'}`, description: `Admin ${userName} berhasil disimpan.` });
    setIsModalOpen(false);
    resetForm();
  };

  const handleDeleteUser = (userId) => {
    const userToDelete = users.find(u => u.id === userId);
    if (userToDelete && userToDelete.email === '<EMAIL>') {
        toast({ title: "Aksi Ditolak", description: "Akun Super Admin utama tidak dapat dihapus.", variant: "destructive" });
        return;
    }
    const updatedUsers = users.filter(u => u.id !== userId);
    setUsers(updatedUsers);
    localStorage.setItem('adminUsers', JSON.stringify(updatedUsers));
    toast({ title: "Admin Dihapus", description: `Admin dengan ID ${userId} telah dihapus.` });
  };
  
  const roles = ['all', 'Superadmin', 'Editor', 'Author', 'Viewer'];
  
  const getRoleIcon = (role) => {
    switch(role) {
      case 'Superadmin': return <ShieldCheck className="h-5 w-5 text-red-400" />;
      case 'Editor': return <ShieldAlert className="h-5 w-5 text-yellow-400" />;
      case 'Author': return <Shield className="h-5 w-5 text-blue-400" />;
      case 'Viewer': return <Shield className="h-5 w-5 text-green-400" />;
      default: return <Shield className="h-5 w-5 text-gray-400" />;
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: { staggerChildren: 0.05 }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.3 } }
  };

  return (
    <div className="space-y-6">
      <motion.div 
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4"
      >
        <h1 className="text-3xl font-bold text-white tracking-tight">Manajemen Admin</h1>
        <Button onClick={() => handleOpenModal()} className="gold-gradient text-black font-semibold hover:opacity-90">
          <UserPlus size={20} className="mr-2" /> Tambah Admin Baru
        </Button>
      </motion.div>

      <motion.div variants={itemVariants} initial="hidden" animate="visible">
        <Card className="bg-gray-800 border-gray-700 text-white shadow-lg">
          <CardHeader>
            <CardTitle>Filter Admin</CardTitle>
            <CardDescription className="text-gray-400">Cari dan filter pengguna admin.</CardDescription>
          </CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="relative">
              <Input 
                type="text"
                placeholder="Cari nama atau email admin..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:border-primary pl-10"
              />
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
            </div>
            <Select value={filterRole} onValueChange={setFilterRole}>
              <SelectTrigger className="bg-gray-700 border-gray-600 text-white focus:border-primary">
                <SelectValue placeholder="Filter Role" />
              </SelectTrigger>
              <SelectContent className="bg-gray-800 text-white border-gray-700">
                {roles.map(role => (
                  <SelectItem key={role} value={role} className="hover:bg-primary/20 focus:bg-primary/20 cursor-pointer">
                    {role === 'all' ? 'Semua Role' : role}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </CardContent>
        </Card>
      </motion.div>

      <motion.div variants={itemVariants} initial="hidden" animate="visible">
        <Card className="bg-gray-800 border-gray-700 text-white shadow-lg overflow-x-auto">
          <CardHeader>
            <CardTitle>Daftar Pengguna Admin</CardTitle>
          </CardHeader>
          <CardContent>
            {filteredUsers.length === 0 ? (
              <p className="text-center text-gray-400 py-8">
                Tidak ada pengguna admin yang cocok dengan filter Anda.
              </p>
            ) : (
            <Table>
              <TableHeader>
                <TableRow className="border-gray-700 hover:bg-gray-700/30">
                  <TableHead className="text-gray-300">Nama</TableHead>
                  <TableHead className="text-gray-300">Email</TableHead>
                  <TableHead className="text-gray-300">Role</TableHead>
                  <TableHead className="text-gray-300">Login Terakhir</TableHead>
                  <TableHead className="text-gray-300">Status</TableHead>
                  <TableHead className="text-gray-300 text-right">Aksi</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredUsers.map((user) => (
                  <TableRow key={user.id} className="border-gray-700 hover:bg-gray-700/30">
                    <TableCell className="font-medium">{user.name}</TableCell>
                    <TableCell>{user.email}</TableCell>
                    <TableCell className="flex items-center">
                      {getRoleIcon(user.role)}
                      <span className="ml-2">{user.role}</span>
                    </TableCell>
                    <TableCell>{user.lastLogin}</TableCell>
                    <TableCell>
                      <span className={`px-2 py-0.5 rounded-full text-xs font-medium
                        ${user.status === 'Active' ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'}`}>
                        {user.status}
                      </span>
                    </TableCell>
                    <TableCell className="text-right">
                      <Button variant="ghost" size="icon" onClick={() => handleOpenModal(user)} className="text-gray-400 hover:text-primary">
                        <Edit size={18} />
                      </Button>
                      <Button variant="ghost" size="icon" onClick={() => handleDeleteUser(user.id)} className="text-gray-400 hover:text-red-400" disabled={user.email === '<EMAIL>'}>
                        <Trash2 size={18} />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            )}
          </CardContent>
        </Card>
      </motion.div>
      
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="bg-gray-800 border-gray-700 text-white">
          <DialogHeader><DialogTitle className="text-primary">{currentUser ? 'Edit Admin' : 'Tambah Admin Baru'}</DialogTitle></DialogHeader>
          <div className="space-y-4 py-3">
            <div><Label htmlFor="userNameModal">Nama Lengkap</Label><Input id="userNameModal" value={userName} onChange={e => setUserName(e.target.value)} className="bg-gray-700 border-gray-600" /></div>
            <div><Label htmlFor="userEmailModal">Alamat Email</Label><Input id="userEmailModal" type="email" value={userEmail} onChange={e => setUserEmail(e.target.value)} className="bg-gray-700 border-gray-600" /></div>
            <div><Label htmlFor="userRoleModal">Peran (Role)</Label>
              <Select value={userRole} onValueChange={setUserRole}>
                <SelectTrigger className="bg-gray-700 border-gray-600"><SelectValue /></SelectTrigger>
                <SelectContent className="bg-gray-800 text-white border-gray-700">
                  {roles.filter(r => r !== 'all').map(role => (
                    <SelectItem key={role} value={role} className="hover:bg-primary/20">{role}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="border-t border-gray-700 pt-4 mt-2">
              <Label className="text-gray-400 flex items-center"><KeyRound size={16} className="mr-2"/> Atur Kata Sandi Baru</Label>
              <p className="text-xs text-gray-500 mb-2">{currentUser ? 'Kosongkan jika tidak ingin mengubah. ' : ''}</p>
              <div><Label htmlFor="userPasswordModal">Kata Sandi Baru (min. 6 karakter)</Label><Input id="userPasswordModal" type="password" value={userPassword} onChange={e => setUserPassword(e.target.value)} className="bg-gray-700 border-gray-600" /></div>
              <div><Label htmlFor="confirmPasswordModal">Konfirmasi Kata Sandi Baru</Label><Input id="confirmPasswordModal" type="password" value={confirmPassword} onChange={e => setConfirmPassword(e.target.value)} className="bg-gray-700 border-gray-600" /></div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => { setIsModalOpen(false); resetForm(); }} className="text-gray-300 border-gray-600">Batal</Button>
            <Button onClick={handleSaveUser} className="gold-gradient text-black"><Save size={16} className="mr-2"/> Simpan Admin</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <motion.p variants={itemVariants} initial="hidden" animate="visible" className="text-center text-gray-500 pt-4">
        🚧 Untuk keamanan produksi, gunakan backend untuk autentikasi dan manajemen pengguna. 🚧
      </motion.p>
    </div>
  );
};

export default AdminUsersPage;
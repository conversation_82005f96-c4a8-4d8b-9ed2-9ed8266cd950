import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card.jsx';
import { Button } from '@/components/ui/button.jsx';
import { Input } from '@/components/ui/input.jsx';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select.jsx';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table.jsx';
import { Download, Search, Mail, MessageCircle as WhatsAppIcon, RotateCcw, Users, Package } from 'lucide-react';
import { motion } from 'framer-motion';
import { useToast } from '@/components/ui/use-toast.js';
import { leadsStorageService } from '@/services/leadsStorageService.js';
import { format } from 'date-fns';
import { id } from 'date-fns/locale';

const AdminLeadsPage = () => {
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterSource, setFilterSource] = useState('all');
  const [leadsData, setLeadsData] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  const fetchLeads = useCallback(() => {
    setIsLoading(true);
    try {
      const allLeads = leadsStorageService.loadAllLeadsAndOrders();
      setLeadsData(allLeads.sort((a,b) => new Date(b.date) - new Date(a.date)));
    } catch (error) {
      console.error('Error fetching leads:', error);
      toast({
        title: "Gagal Memuat Data",
        description: "Terjadi kesalahan saat memuat data leads dan order.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  useEffect(() => {
    fetchLeads();
  }, [fetchLeads]);

  const filteredLeads = leadsData.filter(lead => {
    const searchLower = searchTerm.toLowerCase();
    const matchesSearch = 
      (lead.name && lead.name.toLowerCase().includes(searchLower)) ||
      (lead.email && lead.email.toLowerCase().includes(searchLower)) ||
      (lead.phone && lead.phone.includes(searchTerm)) ||
      (lead.subject && lead.subject.toLowerCase().includes(searchLower)) ||
      (lead.message && lead.message.toLowerCase().includes(searchLower));
    const matchesSource = filterSource === 'all' || (lead.source && lead.source === filterSource);
    return matchesSearch && matchesSource;
  });

  const handleExportCSV = () => {
    toast({
        title: "Fungsi Belum Tersedia",
        description: "🚧 Fitur ini belum diimplementasikan—tapi jangan khawatir! Anda dapat memintanya di prompt berikutnya! 🚀",
        variant: "destructive",
    });
  };

  const handleReplyEmail = (email) => {
    if (!email) {
      toast({ title: "Email Tidak Tersedia", variant: "destructive" });
      return;
    }
    window.open(`mailto:${email}`, '_blank');
    toast({
      title: `Membuka Email Client`,
      description: `Mengarahkan untuk mengirim email ke ${email}.`,
    });
  };

  const handleReplyWhatsApp = (phone) => {
    if (!phone) {
      toast({ title: "Nomor WhatsApp Tidak Tersedia", variant: "destructive" });
      return;
    }
    const cleanPhone = phone.replace(/[^0-9]/g, '');
    const formattedPhone = cleanPhone.startsWith('0') ? '62' + cleanPhone.substring(1) : cleanPhone;
    const whatsappUrl = `https://wa.me/${formattedPhone}`;
    window.open(whatsappUrl, '_blank');
    toast({
      title: `Membuka WhatsApp`,
      description: `Mengarahkan untuk mengirim pesan ke ${phone}.`,
    });
  };
  
  const sources = ['all', ...new Set(leadsData.map(l => l.source).filter(Boolean))];

  return (
    <div className="space-y-6">
      <motion.div 
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4"
      >
        <div>
          <h1 className="text-3xl font-bold text-white tracking-tight">Manajemen Leads & Order</h1>
          <p className="text-gray-400 mt-1 flex items-center">
            <Users size={16} className="mr-2" />
            Total {leadsData.length} data dari semua sumber
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={fetchLeads} variant="outline" className="text-gray-300 border-gray-600 hover:bg-gray-700" disabled={isLoading}>
            <RotateCcw size={18} className="mr-2" /> {isLoading ? 'Memuat...' : 'Muat Ulang'}
          </Button>
          <Button onClick={handleExportCSV} className="gold-gradient text-black font-semibold hover:opacity-90" disabled={filteredLeads.length === 0}>
            <Download size={20} className="mr-2" /> Export ke CSV
          </Button>
        </div>
      </motion.div>

      <motion.div initial={{ opacity: 0, y: 10 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.1 }}>
        <Card className="bg-gray-800 border-gray-700 text-white shadow-lg">
          <CardHeader>
            <CardTitle>Filter & Pencarian</CardTitle>
            <CardDescription className="text-gray-400">Cari dan filter data leads dan order yang masuk.</CardDescription>
          </CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="relative">
              <Input 
                type="text"
                placeholder="Cari nama, email, telepon, subjek..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:border-primary pl-10"
              />
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
            </div>
            <Select value={filterSource} onValueChange={setFilterSource}>
              <SelectTrigger className="bg-gray-700 border-gray-600 text-white focus:border-primary">
                <SelectValue placeholder="Filter Sumber Data" />
              </SelectTrigger>
              <SelectContent className="bg-gray-800 text-white border-gray-700">
                {sources.map(src => (
                  <SelectItem key={src} value={src} className="hover:bg-primary/20 focus:bg-primary/20 cursor-pointer">
                    {src === 'all' ? 'Semua Sumber' : src}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </CardContent>
        </Card>
      </motion.div>

      <motion.div initial={{ opacity: 0, y: 10 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.2 }}>
        <Card className="bg-gray-800 border-gray-700 text-white shadow-lg">
          <CardHeader>
            <CardTitle>Daftar Data Masuk ({filteredLeads.length})</CardTitle>
            <CardDescription className="text-gray-400">
              Kelola dan tindak lanjuti semua data yang masuk.
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="text-center text-gray-400 py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                Memuat data...
              </div>
            ) : filteredLeads.length === 0 ? (
              <p className="text-center text-gray-400 py-8">
                {searchTerm || filterSource !== 'all' 
                  ? 'Tidak ada data yang cocok dengan filter Anda.' 
                  : 'Belum ada data yang masuk.'}
              </p>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow className="border-gray-700 hover:bg-gray-700/30">
                      <TableHead className="text-gray-300">Kontak</TableHead>
                      <TableHead className="text-gray-300 min-w-[250px]">Subjek & Pesan</TableHead>
                      <TableHead className="text-gray-300">Sumber</TableHead>
                      <TableHead className="text-gray-300">Tanggal</TableHead>
                      <TableHead className="text-gray-300 text-right">Aksi</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredLeads.map((lead) => (
                      <TableRow key={lead.id} className="border-gray-700 hover:bg-gray-700/30">
                        <TableCell>
                          <p className="font-medium text-white">{lead.name}</p>
                          <p className="text-sm text-gray-400">{lead.email}</p>
                          <p className="text-sm text-gray-400">{lead.phone}</p>
                        </TableCell>
                        <TableCell>
                          <p className="font-semibold text-gray-200" title={lead.subject}>{lead.subject}</p>
                          <p className="text-sm text-gray-400 max-w-xs truncate" title={lead.message}>{lead.message}</p>
                        </TableCell>
                        <TableCell>
                          <span className={`text-xs px-2 py-1 rounded-full whitespace-nowrap ${
                            lead.type === 'Kontak' ? 'bg-blue-500/20 text-blue-400' :
                            lead.type === 'Janji Temu' ? 'bg-purple-500/20 text-purple-400' :
                            lead.type === 'Order' ? 'bg-green-500/20 text-green-400' :
                            'bg-gray-500/20 text-gray-400'
                          }`}>
                            {lead.source}
                          </span>
                        </TableCell>
                        <TableCell>{format(new Date(lead.date), "d MMM yyyy, HH:mm", { locale: id })}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-1">
                            <Button 
                              variant="ghost" 
                              size="icon" 
                              onClick={() => handleReplyEmail(lead.email)} 
                              className="text-gray-400 hover:text-primary h-8 w-8" 
                              disabled={!lead.email}
                              title="Balas via Email"
                            >
                              <Mail size={16} />
                            </Button>
                            <Button 
                              variant="ghost" 
                              size="icon" 
                              onClick={() => handleReplyWhatsApp(lead.phone)} 
                              className="text-gray-400 hover:text-green-400 h-8 w-8" 
                              disabled={!lead.phone}
                              title="Balas via WhatsApp"
                            >
                              <WhatsAppIcon size={16} />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};

export default AdminLeadsPage;
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card.jsx';
import { Button } from '@/components/ui/button.jsx';
import { Input } from '@/components/ui/input.jsx';
import { Label } from '@/components/ui/label.jsx';
import { Textarea } from '@/components/ui/textarea.jsx';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select.jsx';
import { Save, ImageDown as ImageUp, Link as LinkIcon, MapPin, Info, Trash2, Newspaper as NewspaperIcon, Settings2, MessageCircle } from 'lucide-react';
import { motion } from 'framer-motion';
import { useToast } from '@/components/ui/use-toast.js';
import { useContent } from '@/contexts/ContentContext.jsx';

const AdminSettingsPage = () => {
  const { toast } = useToast();
  const { siteSettings: initialSettings, updateSiteSettings, isLoading } = useContent();
  const [settings, setSettings] = useState(initialSettings);
  
  const [logoFile, setLogoFile] = useState(null);
  const [faviconFile, setFaviconFile] = useState(null);

  useEffect(() => {
    setSettings(initialSettings);
  }, [initialSettings]);

  const handleInputChange = (key, value) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };
  
  const handleSocialLinkChange = (platform, value) => {
    setSettings(prev => ({
      ...prev,
      socialLinks: {
        ...prev.socialLinks,
        [platform]: value,
      },
    }));
  };

  const handleWhatsAppChange = (key, value) => {
    setSettings(prev => ({
      ...prev,
      whatsapp: {
        ...prev.whatsapp,
        [key]: value,
      },
    }));
  };

  const handleFileChange = (e, setFileCallback, previewKey) => {
    const file = e.target.files[0];
    if (file) {
      setFileCallback(file); 
      const reader = new FileReader();
      reader.onloadend = () => {
        setSettings(prev => ({ ...prev, [previewKey]: reader.result })); 
      };
      reader.readAsDataURL(file);
    }
  };
  
  const removeImage = (previewKey, setFileCallback) => {
    setSettings(prev => ({ ...prev, [previewKey]: '' }));
    setFileCallback(null);
    toast({ title: "Gambar Dihapus", description: `Pratinjau gambar telah dihapus.`});
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    updateSiteSettings(settings);
  };
  
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5, ease: "easeOut" } }
  };

  if (isLoading) {
    return <div className="text-center text-white">Memuat pengaturan...</div>;
  }

  return (
    <div className="space-y-6">
      <motion.h1 
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-3xl font-bold text-white tracking-tight"
      >
        Pengaturan Situs
      </motion.h1>

      <form onSubmit={handleSubmit} className="space-y-8">
        <motion.div variants={cardVariants} initial="hidden" animate="visible">
          <Card className="bg-gray-800 border-gray-700 text-white shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center"><Info size={20} className="mr-2 text-primary" /> Informasi Umum</CardTitle>
              <CardDescription className="text-gray-400">Pengaturan dasar untuk identitas situs Anda.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="siteName" className="text-gray-300">Nama Situs</Label>
                <Input id="siteName" value={settings.siteName} onChange={(e) => handleInputChange('siteName', e.target.value)} className="bg-gray-700 border-gray-600 focus:border-primary" />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="logo" className="text-gray-300 flex items-center"><ImageUp size={16} className="mr-1.5 text-primary" /> Logo Situs</Label>
                  <Input id="logo" type="file" accept="image/*" onChange={(e) => handleFileChange(e, setLogoFile, 'logoPreview')} className="bg-gray-700 border-gray-600 file:mr-2 file:py-1.5 file:px-3 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-black hover:file:bg-primary/90" />
                  {settings.logoPreview && (
                    <div className="mt-2 relative w-auto max-w-xs inline-block">
                      <img  src={settings.logoPreview} alt="Logo Preview" className="h-16 w-auto bg-gray-700 p-1 rounded" />
                      <Button type="button" variant="destructive" size="icon" className="absolute -top-2 -right-2 h-6 w-6 z-10" onClick={() => removeImage('logoPreview', setLogoFile)}><Trash2 size={14} /></Button>
                    </div>
                  )}
                </div>
                <div>
                  <Label htmlFor="favicon" className="text-gray-300 flex items-center"><ImageUp size={16} className="mr-1.5 text-primary" /> Favicon</Label>
                  <Input id="favicon" type="file" accept="image/svg+xml, image/png, image/x-icon" onChange={(e) => handleFileChange(e, setFaviconFile, 'faviconPreview')} className="bg-gray-700 border-gray-600 file:mr-2 file:py-1.5 file:px-3 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-black hover:file:bg-primary/90" />
                  {settings.faviconPreview && (
                     <div className="mt-2 relative w-auto max-w-xs inline-block">
                        <img  src={settings.faviconPreview} alt="Favicon Preview" className="h-8 w-8 bg-gray-700 p-1 rounded" />
                        <Button type="button" variant="destructive" size="icon" className="absolute -top-2 -right-2 h-6 w-6 z-10" onClick={() => removeImage('faviconPreview', setFaviconFile)}><Trash2 size={14} /></Button>
                     </div>
                  )}
                </div>
              </div>
              <div>
                <Label htmlFor="contactEmail" className="text-gray-300">Email Kontak Utama</Label>
                <Input id="contactEmail" type="email" value={settings.contactEmail} onChange={(e) => handleInputChange('contactEmail', e.target.value)} className="bg-gray-700 border-gray-600 focus:border-primary" />
              </div>
              <div>
                <Label htmlFor="contactPhone" className="text-gray-300">Telepon Kontak Utama</Label>
                <Input id="contactPhone" type="tel" value={settings.contactPhone} onChange={(e) => handleInputChange('contactPhone', e.target.value)} className="bg-gray-700 border-gray-600 focus:border-primary" />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div variants={cardVariants} initial="hidden" animate="visible">
          <Card className="bg-gray-800 border-gray-700 text-white shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center"><MessageCircle size={20} className="mr-2 text-primary" /> Pengaturan WhatsApp</CardTitle>
              <CardDescription className="text-gray-400">Konfigurasi nomor WhatsApp dan pesan default untuk tombol kontak.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="whatsappNumber" className="text-gray-300">Nomor WhatsApp Utama</Label>
                <Input 
                  id="whatsappNumber" 
                  value={settings.whatsapp?.primaryContactNumber || ''} 
                  onChange={(e) => handleWhatsAppChange('primaryContactNumber', e.target.value)} 
                  placeholder="6281234567890 (format internasional tanpa +)"
                  className="bg-gray-700 border-gray-600 focus:border-primary" 
                />
                <p className="text-xs text-gray-500 mt-1">Format: 62 diikuti nomor tanpa 0 di depan. Contoh: 6281234567890</p>
              </div>
              <div>
                <Label htmlFor="whatsappMessage" className="text-gray-300">Pesan Default WhatsApp</Label>
                <Textarea 
                  id="whatsappMessage" 
                  value={settings.whatsapp?.ctaMessage || ''} 
                  onChange={(e) => handleWhatsAppChange('ctaMessage', e.target.value)} 
                  rows={3} 
                  placeholder="Pesan yang akan muncul saat pengunjung mengklik tombol WhatsApp"
                  className="bg-gray-700 border-gray-600 focus:border-primary" 
                />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div variants={cardVariants} initial="hidden" animate="visible">
          <Card className="bg-gray-800 border-gray-700 text-white shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center"><LinkIcon size={20} className="mr-2 text-primary" /> Tautan Sosial Media</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {Object.entries(settings.socialLinks).map(([platform, link]) => (
                <div key={platform}>
                  <Label htmlFor={`social-${platform}`} className="text-gray-300 capitalize">{platform}</Label>
                  <Input id={`social-${platform}`} value={link} onChange={(e) => handleSocialLinkChange(platform, e.target.value)} className="bg-gray-700 border-gray-600 focus:border-primary" />
                </div>
              ))}
            </CardContent>
          </Card>
        </motion.div>

        <motion.div variants={cardVariants} initial="hidden" animate="visible">
          <Card className="bg-gray-800 border-gray-700 text-white shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center"><MapPin size={20} className="mr-2 text-primary" /> Informasi Lokasi Kantor</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="jakartaAddress" className="text-gray-300">Alamat Kantor Jakarta</Label>
                <Textarea id="jakartaAddress" value={settings.jakartaAddress} onChange={(e) => handleInputChange('jakartaAddress', e.target.value)} rows={3} className="bg-gray-700 border-gray-600 focus:border-primary" />
              </div>
              <div>
                <Label htmlFor="makkahAddress" className="text-gray-300">Alamat Kantor Makkah</Label>
                <Textarea id="makkahAddress" value={settings.makkahAddress} onChange={(e) => handleInputChange('makkahAddress', e.target.value)} rows={3} className="bg-gray-700 border-gray-600 focus:border-primary" />
              </div>
            </CardContent>
          </Card>
        </motion.div>
        
        <motion.div variants={cardVariants} initial="hidden" animate="visible">
          <Card className="bg-gray-800 border-gray-700 text-white shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center"><Settings2 size={20} className="mr-2 text-primary" /> SEO Default</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="defaultSeoTitle" className="text-gray-300">Judul SEO Default</Label>
                <Input id="defaultSeoTitle" value={settings.defaultSeoTitle} onChange={(e) => handleInputChange('defaultSeoTitle', e.target.value)} className="bg-gray-700 border-gray-600 focus:border-primary" />
              </div>
              <div>
                <Label htmlFor="defaultMetaDesc" className="text-gray-300">Meta Deskripsi Default</Label>
                <Textarea id="defaultMetaDesc" value={settings.defaultMetaDesc} onChange={(e) => handleInputChange('defaultMetaDesc', e.target.value)} rows={3} className="bg-gray-700 border-gray-600 focus:border-primary" />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div variants={cardVariants} initial="hidden" animate="visible">
          <Card className="bg-gray-800 border-gray-700 text-white shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center"><NewspaperIcon size={20} className="mr-2 text-primary" /> Pengaturan Blog</CardTitle>
              <CardDescription className="text-gray-400">Konfigurasi tampilan dan fungsionalitas halaman blog.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="blogPostsPerPage" className="text-gray-300">Jumlah Post per Halaman</Label>
                <Input 
                  id="blogPostsPerPage" 
                  type="number" 
                  min="1" 
                  value={settings.blogPostsPerPage} 
                  onChange={(e) => handleInputChange('blogPostsPerPage', parseInt(e.target.value, 10) || 1)} 
                  className="bg-gray-700 border-gray-600 focus:border-primary" 
                />
              </div>
              <div>
                <Label htmlFor="blogDefaultLayout" className="text-gray-300">Layout Default Halaman Blog</Label>
                <Select value={settings.blogDefaultLayout} onValueChange={(value) => handleInputChange('blogDefaultLayout', value)}>
                  <SelectTrigger className="bg-gray-700 border-gray-600 focus:border-primary">
                    <SelectValue placeholder="Pilih layout" />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-800 text-white border-gray-700">
                    <SelectItem value="grid" className="hover:bg-primary/20 focus:bg-primary/20">Grid</SelectItem>
                    <SelectItem value="list" className="hover:bg-primary/20 focus:bg-primary/20">List</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="blogEnableComments" className="text-gray-300">Aktifkan Komentar (Simulasi)</Label>
                <Select value={settings.blogEnableComments.toString()} onValueChange={(value) => handleInputChange('blogEnableComments', value === 'true')}>
                  <SelectTrigger className="bg-gray-700 border-gray-600 focus:border-primary">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-800 text-white border-gray-700">
                    <SelectItem value="true" className="hover:bg-primary/20 focus:bg-primary/20">Ya, Aktifkan</SelectItem>
                    <SelectItem value="false" className="hover:bg-primary/20 focus:bg-primary/20">Tidak, Nonaktifkan</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-gray-500 mt-1">Fitur komentar memerlukan integrasi backend (misalnya Disqus, Supabase).</p>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div variants={cardVariants} initial="hidden" animate="visible" className="flex justify-end">
          <Button type="submit" className="gold-gradient text-black font-semibold hover:opacity-90 px-8 py-3 text-base">
            <Save size={20} className="mr-2" /> Simpan Pengaturan
          </Button>
        </motion.div>
      </form>
      
      <motion.div variants={cardVariants} initial="hidden" animate="visible">
        <Card className="bg-green-600/20 border-green-500 text-green-300 shadow-lg">
          <CardContent className="pt-6">
            <p className="text-sm">
              <strong>✅ Sinkronisasi Aktif:</strong> Semua pengaturan yang Anda ubah di sini akan langsung tersinkronisasi dengan tampilan website, termasuk nomor WhatsApp, logo, dan informasi kontak.
            </p>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};

export default AdminSettingsPage;
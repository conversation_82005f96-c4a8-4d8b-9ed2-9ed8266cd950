import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardT<PERSON>le, CardFooter } from '@/components/ui/card.jsx';
import { Button } from '@/components/ui/button.jsx';
import { Input } from '@/components/ui/input.jsx';
import { Label } from '@/components/ui/label.jsx';
import { Textarea } from '@/components/ui/textarea.jsx';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog.jsx";
import { PlusCircle, Edit, Trash2, Search, Package as PackageIcon, Save, DollarSign, ListChecks, ListX, Info } from 'lucide-react';
import { motion } from 'framer-motion';
import { useToast } from '@/components/ui/use-toast.js';
import { cn } from '@/lib/utils';
import { useContent } from '@/contexts/ContentContext.jsx';

const AdminPricingPackagesPage = () => {
  const { toast } = useToast();
  const { pricingPackages, updatePricingPackages, isLoading } = useContent();
  const [searchTerm, setSearchTerm] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentPackage, setCurrentPackage] = useState(null);
  
  const [packageName, setPackageName] = useState('');
  const [packageDescription, setPackageDescription] = useState('');
  const [packageGradient, setPackageGradient] = useState('bg-gradient-to-br from-slate-700 via-slate-800 to-slate-900');
  const [packageTextColor, setPackageTextColor] = useState('text-slate-100');
  const [packageButtonClass, setPackageButtonClass] = useState('bg-slate-600 hover:bg-slate-500 text-white');
  const [packagePopularButtonClass, setPackagePopularButtonClass] = useState('gold-gradient text-black shadow-lg hover:shadow-amber-500/50');
  const [packageIsPopular, setPackageIsPopular] = useState(false);
  const [packagePaxPricing, setPackagePaxPricing] = useState([{ pax: '', price: '' }]);
  const [packageIncludes, setPackageIncludes] = useState(['']);
  const [packageExcludes, setPackageExcludes] = useState(['']);
  const [packageNotes, setPackageNotes] = useState(['']);

  const filteredPackages = pricingPackages.filter(pkg =>
    pkg.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const resetForm = () => {
    setPackageName(''); setPackageDescription('');
    setPackageGradient('bg-gradient-to-br from-slate-700 via-slate-800 to-slate-900');
    setPackageTextColor('text-slate-100'); setPackageButtonClass('bg-slate-600 hover:bg-slate-500 text-white');
    setPackagePopularButtonClass('gold-gradient text-black shadow-lg hover:shadow-amber-500/50');
    setPackageIsPopular(false); setPackagePaxPricing([{ pax: '', price: '' }]);
    setPackageIncludes(['']); setPackageExcludes(['']); setPackageNotes(['']);
  };
  
  const handleOpenModal = (pkg = null) => {
    setCurrentPackage(pkg);
    if (pkg) {
      setPackageName(pkg.name); setPackageDescription(pkg.description);
      setPackageGradient(pkg.gradientClass); setPackageTextColor(pkg.textColor);
      setPackageButtonClass(pkg.buttonClass); setPackagePopularButtonClass(pkg.popularButtonClass);
      setPackageIsPopular(pkg.popular);
      setPackagePaxPricing(pkg.paxPricing.length > 0 ? pkg.paxPricing : [{ pax: '', price: '' }]);
      setPackageIncludes(pkg.includes.length > 0 ? pkg.includes : ['']);
      setPackageExcludes(pkg.excludes.length > 0 ? pkg.excludes : ['']);
      setPackageNotes(pkg.notes.length > 0 ? pkg.notes : ['']);
    } else {
      resetForm();
    }
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false); setCurrentPackage(null); resetForm();
  };

  const handleArrayFieldChange = (setter, index, value) => setter(prev => prev.map((item, i) => i === index ? value : item));
  const addArrayField = (setter, defaultValue) => setter(prev => [...prev, defaultValue]);
  const removeArrayField = (setter, index) => setter(prev => prev.filter((_, i) => i !== index));

  const handlePaxPricingChange = (index, field, value) => {
    const updatedPaxPricing = [...packagePaxPricing];
    updatedPaxPricing[index][field] = field === 'price' ? Number(value) : value;
    setPackagePaxPricing(updatedPaxPricing);
  };

  const handleSavePackage = () => {
    if (!packageName) {
      toast({ title: "Nama Paket Wajib Diisi", variant: "destructive" });
      return;
    }
    const newPackageData = {
      id: currentPackage ? currentPackage.id : `${packageName.toLowerCase().replace(/\s+/g, '-')}-${Date.now()}`,
      name: packageName, description: packageDescription, gradientClass: packageGradient,
      textColor: packageTextColor, buttonClass: packageButtonClass, popularButtonClass: packagePopularButtonClass,
      popular: packageIsPopular, paxPricing: packagePaxPricing.filter(p => p.pax && p.price),
      includes: packageIncludes.filter(item => item.trim() !== ''),
      excludes: packageExcludes.filter(item => item.trim() !== ''),
      notes: packageNotes.filter(item => item.trim() !== ''),
    };
    const updatedPackages = currentPackage ? pricingPackages.map(p => p.id === currentPackage.id ? newPackageData : p) : [...pricingPackages, newPackageData];
    updatePricingPackages(updatedPackages);
    handleCloseModal();
  };

  const handleDeletePackage = (packageId) => {
    const updatedPackages = pricingPackages.filter(p => p.id !== packageId);
    updatePricingPackages(updatedPackages);
  };

  const containerVariants = { hidden: { opacity: 0 }, visible: { opacity: 1, transition: { staggerChildren: 0.05 } } };
  const itemVariants = { hidden: { opacity: 0, y: 20 }, visible: { opacity: 1, y: 0, transition: { duration: 0.3 } } };

  if (isLoading) {
    return <div className="text-center text-white">Memuat konten...</div>;
  }

  return (
    <div className="space-y-6">
      <motion.div initial={{ opacity: 0, y: -20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }} className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h1 className="text-3xl font-bold text-white tracking-tight flex items-center"><PackageIcon size={30} className="mr-3 text-primary" /> Manajemen Paket Harga</h1>
        <Button onClick={() => handleOpenModal()} className="gold-gradient text-black font-semibold hover:opacity-90"><PlusCircle size={20} className="mr-2" /> Tambah Paket Baru</Button>
      </motion.div>

      <motion.div variants={itemVariants} initial="hidden" animate="visible">
        <Card className="bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-700">
          <div className="relative mb-6">
            <Input type="text" placeholder="Cari nama paket..." value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} className="bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:border-primary pl-10" />
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
          </div>

          {filteredPackages.length === 0 ? (
            <motion.p variants={itemVariants} className="text-center text-gray-400 py-8">Tidak ada paket yang cocok atau belum ada data paket.</motion.p>
          ) : (
            <motion.div variants={containerVariants} initial="hidden" animate="visible" className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredPackages.map(pkg => (
                <motion.div key={pkg.id} variants={itemVariants}>
                  <Card className={cn("border text-white shadow-md hover:shadow-primary/20 transition-shadow duration-300 flex flex-col h-full", pkg.gradientClass, pkg.popular ? 'border-primary' : 'border-gray-600')}>
                    <CardHeader className="p-4">
                      <CardTitle className={cn("text-xl font-semibold truncate", pkg.textColor)} title={pkg.name}>{pkg.name}</CardTitle>
                      {pkg.popular && <span className="text-xs font-medium px-2 py-0.5 rounded-full bg-primary text-black inline-block mt-1">Populer</span>}
                    </CardHeader>
                    <CardContent className="flex-grow p-4 pt-0">
                      <p className={cn("text-sm line-clamp-3", pkg.textColor === 'text-slate-100' ? 'text-gray-300' : `${pkg.textColor} opacity-90`)}>{pkg.description}</p>
                    </CardContent>
                    <CardFooter className="flex justify-end space-x-2 border-t p-3" style={{borderColor: pkg.textColor === 'text-slate-100' ? 'rgba(255,255,255,0.2)' : 'rgba(0,0,0,0.2)'}}>
                      <Button variant="outline" size="sm" onClick={() => handleOpenModal(pkg)} className={cn("text-xs px-2 py-1", pkg.textColor === 'text-slate-100' ? 'text-primary border-primary hover:bg-primary/10 hover:text-primary' : `${pkg.textColor} border-${pkg.textColor.split('-')[1]}-400 hover:bg-${pkg.textColor.split('-')[1]}-400/10`)}>
                        <Edit size={14} className="mr-1" /> Edit
                      </Button>
                      <Button variant="destructiveOutline" size="sm" onClick={() => handleDeletePackage(pkg.id)} className="text-red-400 border-red-400 hover:bg-red-400/10 hover:text-red-400 text-xs px-2 py-1">
                        <Trash2 size={14} className="mr-1" /> Hapus
                      </Button>
                    </CardFooter>
                  </Card>
                </motion.div>
              ))}
            </motion.div>
          )}
        </Card>
      </motion.div>

      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="bg-gray-800 border-gray-700 text-white sm:max-w-2xl">
          <DialogHeader>
            <DialogTitle className="text-primary text-xl">{currentPackage ? 'Edit Paket Harga' : 'Tambah Paket Harga Baru'}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4 max-h-[75vh] overflow-y-auto pr-3">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div><Label htmlFor="packageName">Nama Paket</Label><Input id="packageName" value={packageName} onChange={(e) => setPackageName(e.target.value)} className="bg-gray-700 border-gray-600" /></div>
              <div><Label htmlFor="packageIsPopular">Populer?</Label><Input type="checkbox" id="packageIsPopular" checked={packageIsPopular} onChange={(e) => setPackageIsPopular(e.target.checked)} className="ml-2 h-5 w-5 accent-primary" /></div>
            </div>
            <div><Label htmlFor="packageDescription">Deskripsi</Label><Textarea id="packageDescription" value={packageDescription} onChange={(e) => setPackageDescription(e.target.value)} className="bg-gray-700 border-gray-600" /></div>
            
            <Card className="bg-gray-700/50 p-4 border-gray-600">
              <CardTitle className="text-md mb-2 text-primary flex items-center"><DollarSign size={18} className="mr-2"/> Harga per Pax (USD)</CardTitle>
              {packagePaxPricing.map((tier, index) => (
                <div key={index} className="flex items-center gap-2 mb-2">
                  <Input placeholder="Contoh: 44 - 47" value={tier.pax} onChange={(e) => handlePaxPricingChange(index, 'pax', e.target.value)} className="bg-gray-600 border-gray-500" />
                  <Input type="number" placeholder="Harga" value={tier.price} onChange={(e) => handlePaxPricingChange(index, 'price', e.target.value)} className="bg-gray-600 border-gray-500" />
                  {packagePaxPricing.length > 1 && <Button type="button" variant="destructiveOutline" size="icon" onClick={() => removeArrayField(setPackagePaxPricing, index)} className="h-8 w-8"><Trash2 size={14}/></Button>}
                </div>
              ))}
              <Button type="button" variant="outline" size="sm" onClick={() => addArrayField(setPackagePaxPricing, { pax: '', price: '' })} className="text-primary border-primary hover:bg-primary/10 mt-1">Tambah Tier Harga</Button>
            </Card>

            {[
              {label: "Termasuk (Includes)", state: packageIncludes, setter: setPackageIncludes, icon: <ListChecks size={18} className="mr-2"/>},
              {label: "Tidak Termasuk (Excludes)", state: packageExcludes, setter: setPackageExcludes, icon: <ListX size={18} className="mr-2"/>},
              {label: "Catatan (Notes)", state: packageNotes, setter: setPackageNotes, icon: <Info size={18} className="mr-2"/>}
            ].map(section => (
              <Card key={section.label} className="bg-gray-700/50 p-4 border-gray-600">
                <CardTitle className="text-md mb-2 text-primary flex items-center">{section.icon} {section.label}</CardTitle>
                {section.state.map((item, index) => (
                  <div key={index} className="flex items-center gap-2 mb-2">
                    <Textarea value={item} onChange={(e) => handleArrayFieldChange(section.setter, index, e.target.value)} rows={1} className="bg-gray-600 border-gray-500" />
                    {section.state.length > 1 && <Button type="button" variant="destructiveOutline" size="icon" onClick={() => removeArrayField(section.setter, index)} className="h-8 w-8"><Trash2 size={14}/></Button>}
                  </div>
                ))}
                <Button type="button" variant="outline" size="sm" onClick={() => addArrayField(section.setter, '')} className="text-primary border-primary hover:bg-primary/10 mt-1">Tambah Item</Button>
              </Card>
            ))}
            
            <details className="bg-gray-700/30 p-3 rounded-md">
                <summary className="text-sm text-gray-400 cursor-pointer hover:text-primary">Pengaturan Tampilan (Opsional)</summary>
                <div className="mt-3 space-y-3">
                    <div><Label htmlFor="packageGradient">Kelas Gradien Latar</Label><Input id="packageGradient" value={packageGradient} onChange={(e) => setPackageGradient(e.target.value)} className="bg-gray-600 border-gray-500" /></div>
                    <div><Label htmlFor="packageTextColor">Kelas Warna Teks</Label><Input id="packageTextColor" value={packageTextColor} onChange={(e) => setPackageTextColor(e.target.value)} className="bg-gray-600 border-gray-500" /></div>
                    <div><Label htmlFor="packageButtonClass">Kelas Tombol Biasa</Label><Input id="packageButtonClass" value={packageButtonClass} onChange={(e) => setPackageButtonClass(e.target.value)} className="bg-gray-600 border-gray-500" /></div>
                    <div><Label htmlFor="packagePopularButtonClass">Kelas Tombol Populer</Label><Input id="packagePopularButtonClass" value={packagePopularButtonClass} onChange={(e) => setPackagePopularButtonClass(e.target.value)} className="bg-gray-600 border-gray-500" /></div>
                </div>
            </details>

          </div>
          <div className="flex justify-end space-x-2 pt-4 border-t border-gray-700">
            <Button variant="outline" onClick={handleCloseModal} className="text-gray-300 border-gray-600 hover:bg-gray-700">Batal</Button>
            <Button onClick={handleSavePackage} className="gold-gradient text-black font-semibold hover:opacity-90"><Save size={18} className="mr-2" /> Simpan Paket</Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AdminPricingPackagesPage;
import React, { useState, useEffect, useContext } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card.jsx';
import { Button } from '@/components/ui/button.jsx';
import { Textarea } from '@/components/ui/textarea.jsx';
import { Label } from '@/components/ui/label.jsx';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select.jsx';
import { Save, Languages, AlertTriangle } from 'lucide-react';
import { motion } from 'framer-motion';
import { useToast } from '@/components/ui/use-toast.js';
import { LanguageContext } from '@/contexts/LanguageContext.jsx';

const AdminLanguageSettingsPage = () => {
  const { toast } = useToast();
  const { translations: currentContextTranslations, language: currentLangContext, changeLanguage, updateContextTranslations } = useContext(LanguageContext);
  
  const [selectedLang, setSelectedLang] = useState(currentLangContext);
  const [translations, setTranslations] = useState({});
  const [isLoading, setIsLoading] = useState(true);

  const availableLangs = [
    { code: 'id', name: 'Bahasa Indonesia' },
    { code: 'en', name: 'English' },
    { code: 'ar', name: 'العربية (Arabic)' },
  ];

  useEffect(() => {
    setIsLoading(true);
    try {
      const storedTranslations = JSON.parse(localStorage.getItem(`translations_${selectedLang}`)) || {};
      if (Object.keys(storedTranslations).length === 0 && selectedLang === currentLangContext) {
        setTranslations(currentContextTranslations);
      } else if (Object.keys(storedTranslations).length > 0) {
        setTranslations(storedTranslations);
      } else {
        // Fallback for uninitialized languages in localStorage
        const fallbackTranslations = selectedLang === 'id' ? JSON.parse(localStorage.getItem('translations_id_default') || '{}') 
                                   : selectedLang === 'en' ? JSON.parse(localStorage.getItem('translations_en_default') || '{}') 
                                   : selectedLang === 'ar' ? JSON.parse(localStorage.getItem('translations_ar_default') || '{}') 
                                   : {};
        setTranslations(fallbackTranslations);
      }
    } catch (error) {
      console.error("Error loading translations:", error);
      setTranslations({}); 
      toast({ title: "Gagal Memuat Terjemahan", description: "Terjadi kesalahan saat memuat data terjemahan.", variant: "destructive"});
    }
    setIsLoading(false);
  }, [selectedLang, currentLangContext, currentContextTranslations, toast]);

  const handleTranslationChange = (key, value) => {
    setTranslations(prev => ({ ...prev, [key]: value }));
  };

  const handleSaveTranslations = () => {
    localStorage.setItem(`translations_${selectedLang}`, JSON.stringify(translations));
    
    // Update context if the current language was edited
    if (selectedLang === currentLangContext) {
      updateContextTranslations(selectedLang, translations);
    }

    toast({
      title: "Terjemahan Disimpan (Simulasi)",
      description: `Terjemahan untuk ${availableLangs.find(l => l.code === selectedLang)?.name} telah disimpan ke localStorage.`,
      variant: "default",
    });
  };
  
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5, ease: "easeOut" } }
  };

  return (
    <div className="space-y-6">
      <motion.h1 
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-3xl font-bold text-white tracking-tight flex items-center"
      >
        <Languages size={30} className="mr-3 text-primary" /> Pengaturan Bahasa & Terjemahan
      </motion.h1>

      <motion.div variants={cardVariants} initial="hidden" animate="visible">
        <Card className="bg-gray-800 border-gray-700 text-white shadow-lg">
          <CardHeader>
            <CardTitle>Pilih Bahasa untuk Diedit</CardTitle>
            <CardDescription className="text-gray-400">Pilih bahasa dari daftar untuk melihat dan mengedit terjemahannya.</CardDescription>
          </CardHeader>
          <CardContent>
            <Select value={selectedLang} onValueChange={setSelectedLang}>
              <SelectTrigger className="w-full md:w-1/2 bg-gray-700 border-gray-600 focus:border-primary">
                <SelectValue placeholder="Pilih bahasa..." />
              </SelectTrigger>
              <SelectContent className="bg-gray-800 text-white border-gray-700">
                {availableLangs.map(lang => (
                  <SelectItem key={lang.code} value={lang.code} className="hover:bg-primary/20 focus:bg-primary/20 cursor-pointer">
                    {lang.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </CardContent>
        </Card>
      </motion.div>
      
      <motion.div variants={cardVariants} initial="hidden" animate="visible">
        <Card className="bg-gray-800 border-gray-700 text-white shadow-lg">
          <CardHeader>
            <CardTitle>Editor Terjemahan untuk: <span className="text-primary">{availableLangs.find(l => l.code === selectedLang)?.name}</span></CardTitle>
            <CardDescription className="text-gray-400">
              <AlertTriangle className="inline h-4 w-4 mr-1 text-yellow-400" />
              Modifikasi langsung file JSON dari frontend tidak disarankan untuk produksi. Ini adalah simulasi menggunakan localStorage.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4 max-h-[60vh] overflow-y-auto pr-2">
            {isLoading ? (
              <p className="text-gray-400">Memuat terjemahan...</p>
            ) : Object.keys(translations).length > 0 ? (
              Object.entries(translations).map(([key, value]) => (
                <div key={key} className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-2 items-start">
                  <div>
                    <Label htmlFor={`translation-${key}`} className="text-gray-400 text-sm break-all">{key}</Label>
                  </div>
                  <Textarea 
                    id={`translation-${key}`} 
                    value={value} 
                    onChange={(e) => handleTranslationChange(key, e.target.value)} 
                    rows={typeof value === 'string' && value.length > 50 ? 3 : 1} 
                    className="bg-gray-700 border-gray-600 focus:border-primary text-sm"
                  />
                </div>
              ))
            ) : (
               <p className="text-gray-400 text-center py-4">Tidak ada terjemahan yang dimuat untuk bahasa ini, atau file terjemahan kosong.</p>
            )}
          </CardContent>
          {Object.keys(translations).length > 0 && !isLoading && (
            <CardFooter className="border-t border-gray-700 pt-6 mt-4">
                <Button onClick={handleSaveTranslations} className="gold-gradient text-black font-semibold hover:opacity-90">
                <Save size={18} className="mr-2" /> Simpan Perubahan (Simulasi)
                </Button>
            </CardFooter>
          )}
        </Card>
      </motion.div>
      <p className="text-center text-gray-500 pt-4">
        🚧 Fitur untuk menambah kunci terjemahan baru atau mengelola file secara langsung akan memerlukan backend. 🚧
      </p>
    </div>
  );
};

export default AdminLanguageSettingsPage;
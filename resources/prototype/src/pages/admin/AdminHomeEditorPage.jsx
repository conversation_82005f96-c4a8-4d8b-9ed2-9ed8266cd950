import React, { useState, useEffect } from 'react';
import { Card, CardContent, Card<PERSON>eader, CardTitle, CardDescription } from '@/components/ui/card.jsx';
import { Button } from '@/components/ui/button.jsx';
import { Input } from '@/components/ui/input.jsx';
import { Label } from '@/components/ui/label.jsx';
import { Textarea } from '@/components/ui/textarea.jsx';
import { Save, ImageDown as ImageUp, Trash2 } from 'lucide-react';
import { motion } from 'framer-motion';
import { useToast } from '@/components/ui/use-toast.js';
import { useContent } from '@/contexts/ContentContext.jsx';

const AdminHomeEditorPage = () => {
  const { toast } = useToast();
  const { homeContent: initialContent, updateHomeContent, isLoading } = useContent();
  const [homeContent, setHomeContent] = useState(initialContent);

  useEffect(() => {
    setHomeContent(initialContent);
  }, [initialContent]);

  const handleInputChange = (section, key, value, index = null) => {
    setHomeContent(prev => {
      const newContent = JSON.parse(JSON.stringify(prev)); // Deep copy
      if (index !== null) {
        newContent[section][index][key] = value;
      } else {
        newContent[section][key] = value;
      }
      return newContent;
    });
  };

  const handleImageUpload = (section, key, event, index = null) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        handleInputChange(section, key, reader.result, index);
        toast({ title: "Gambar Diperbarui", description: `Pratinjau gambar untuk ${section} telah diubah.` });
      };
      reader.readAsDataURL(file);
    }
  };
  
  const removeImage = (section, key, index = null) => {
     handleInputChange(section, key, '', index);
     toast({ title: "Gambar Dihapus", description: `Gambar untuk ${section} telah dihapus.`});
  };

  const saveHomeContent = () => {
    updateHomeContent(homeContent);
  };
  
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5, ease: "easeOut" } }
  };

  if (isLoading) {
    return <div className="text-center text-white">Memuat konten...</div>;
  }

  return (
    <div className="space-y-6">
      <motion.h1 
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-3xl font-bold text-white tracking-tight"
      >
        Editor Konten Beranda
      </motion.h1>

      <motion.div variants={cardVariants} initial="hidden" animate="visible">
        <Card className="bg-gray-800 border-gray-700 text-white shadow-lg">
          <CardHeader>
            <CardTitle>Bagian Hero</CardTitle>
            <CardDescription className="text-gray-400">Edit judul, subjudul, dan gambar latar belakang untuk bagian hero.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="heroTitle" className="text-gray-300">Judul Hero</Label>
              <Input id="heroTitle" value={homeContent.hero.title} onChange={(e) => handleInputChange('hero', 'title', e.target.value)} className="bg-gray-700 border-gray-600 focus:border-primary" />
            </div>
            <div>
              <Label htmlFor="heroSubtitle" className="text-gray-300">Subjudul Hero</Label>
              <Textarea id="heroSubtitle" value={homeContent.hero.subtitle} onChange={(e) => handleInputChange('hero', 'subtitle', e.target.value)} rows={3} className="bg-gray-700 border-gray-600 focus:border-primary" />
            </div>
            <div>
              <Label htmlFor="heroImage" className="text-gray-300 flex items-center"><ImageUp size={16} className="mr-1.5 text-primary" /> Gambar Latar Hero</Label>
              <Input id="heroImage" type="file" accept="image/*" onChange={(e) => handleImageUpload('hero', 'image', e)} className="bg-gray-700 border-gray-600 file:mr-2 file:py-1.5 file:px-3 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-black hover:file:bg-primary/90" />
              {homeContent.hero.image && (
                <div className="mt-2 relative w-full max-w-xs">
                  <img  src={homeContent.hero.image} alt="Preview Hero" className="rounded-md h-24 w-auto object-cover bg-gray-700 p-1" />
                   <Button type="button" variant="destructive" size="icon" className="absolute top-1 right-1 h-6 w-6" onClick={() => removeImage('hero', 'image')}><Trash2 size={14} /></Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      <motion.div variants={cardVariants} initial="hidden" animate="visible">
        <Card className="bg-gray-800 border-gray-700 text-white shadow-lg">
          <CardHeader>
            <CardTitle>Bagian Fitur Unggulan</CardTitle>
            <CardDescription className="text-gray-400">Edit judul dan deskripsi untuk setiap fitur.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {homeContent.features.map((feature, index) => (
              <div key={index} className="p-4 border border-gray-700 rounded-md space-y-3">
                <p className="text-sm font-semibold text-primary">Fitur #{index + 1}</p>
                <div>
                  <Label htmlFor={`featureTitle-${index}`} className="text-gray-300">Judul Fitur</Label>
                  <Input id={`featureTitle-${index}`} value={feature.title} onChange={(e) => handleInputChange('features', 'title', e.target.value, index)} className="bg-gray-700 border-gray-600 focus:border-primary" />
                </div>
                <div>
                  <Label htmlFor={`featureDesc-${index}`} className="text-gray-300">Deskripsi Fitur</Label>
                  <Textarea id={`featureDesc-${index}`} value={feature.description} onChange={(e) => handleInputChange('features', 'description', e.target.value, index)} rows={2} className="bg-gray-700 border-gray-600 focus:border-primary" />
                </div>
              </div>
            ))}
             <p className="text-xs text-gray-500 mt-1">🚧 Penambahan/penghapusan fitur akan diimplementasikan. 🚧</p>
          </CardContent>
        </Card>
      </motion.div>
      
      <motion.div variants={cardVariants} initial="hidden" animate="visible">
        <Card className="bg-gray-800 border-gray-700 text-white shadow-lg">
          <CardHeader>
            <CardTitle>Bagian Call to Action (CTA)</CardTitle>
            <CardDescription className="text-gray-400">Edit judul, subjudul, dan gambar latar untuk bagian CTA.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="ctaTitle" className="text-gray-300">Judul CTA</Label>
              <Input id="ctaTitle" value={homeContent.callToAction.title} onChange={(e) => handleInputChange('callToAction', 'title', e.target.value)} className="bg-gray-700 border-gray-600 focus:border-primary" />
            </div>
            <div>
              <Label htmlFor="ctaSubtitle" className="text-gray-300">Subjudul CTA</Label>
              <Textarea id="ctaSubtitle" value={homeContent.callToAction.subtitle} onChange={(e) => handleInputChange('callToAction', 'subtitle', e.target.value)} rows={3} className="bg-gray-700 border-gray-600 focus:border-primary" />
            </div>
            <div>
              <Label htmlFor="ctaImage" className="text-gray-300 flex items-center"><ImageUp size={16} className="mr-1.5 text-primary" /> Gambar Latar CTA</Label>
              <Input id="ctaImage" type="file" accept="image/*" onChange={(e) => handleImageUpload('callToAction', 'image', e)} className="bg-gray-700 border-gray-600 file:mr-2 file:py-1.5 file:px-3 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-black hover:file:bg-primary/90" />
              {homeContent.callToAction.image && (
                <div className="mt-2 relative w-full max-w-xs">
                  <img src={homeContent.callToAction.image} alt="Preview CTA" className="rounded-md h-24 w-auto object-cover bg-gray-700 p-1" />
                   <Button type="button" variant="destructive" size="icon" className="absolute top-1 right-1 h-6 w-6" onClick={() => removeImage('callToAction', 'image')}><Trash2 size={14} /></Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      <motion.div variants={cardVariants} initial="hidden" animate="visible" className="flex justify-end mt-8">
        <Button onClick={saveHomeContent} className="gold-gradient text-black font-semibold hover:opacity-90 px-8 py-3 text-base">
          <Save size={20} className="mr-2" /> Simpan Konten Beranda
        </Button>
      </motion.div>
      <p className="text-center text-gray-500 pt-4">
        🚧 Pengelolaan bagian Testimoni dan Logo Mitra akan ditangani di halaman Manajemen Mitra. 🚧
      </p>
    </div>
  );
};

export default AdminHomeEditorPage;
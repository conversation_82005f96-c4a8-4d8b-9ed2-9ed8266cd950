import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card.jsx';
import { Button } from '@/components/ui/button.jsx';
import { Input } from '@/components/ui/input.jsx';
import { Label } from '@/components/ui/label.jsx';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter as DialogPrimitiveFooter } from "@/components/ui/dialog.jsx";
import { FileText, Edit, Eye, PlusCircle, Trash2, Save, HelpCircle, ListPlus } from 'lucide-react';
import { motion } from 'framer-motion';
import { useToast } from '@/components/ui/use-toast.js';
import RichTextEditor from '@/components/admin/RichTextEditor.jsx';
import { useContent } from '@/contexts/ContentContext.jsx';

const AdminContentPage = () => {
  const { toast } = useToast();
  const { pages, updatePages, faqItems, updateFaqItems, isLoading } = useContent();
  
  const [isPageModalOpen, setIsPageModalOpen] = useState(false);
  const [isFaqModalOpen, setIsFaqModalOpen] = useState(false);
  
  const [currentPage, setCurrentPage] = useState(null);
  const [pageTitle, setPageTitle] = useState('');
  const [pageSlug, setPageSlug] = useState('');
  const [pageContent, setPageContent] = useState('');
  const [pageStatus, setPageStatus] = useState('Published');

  const [currentFaqItem, setCurrentFaqItem] = useState(null);
  const [faqQuestionId, setFaqQuestionId] = useState('');
  const [faqAnswerId, setFaqAnswerId] = useState('');
  const [faqQuestionEn, setFaqQuestionEn] = useState('');
  const [faqAnswerEn, setFaqAnswerEn] = useState('');
  const [faqQuestionAr, setFaqQuestionAr] = useState('');
  const [faqAnswerAr, setFaqAnswerAr] = useState('');

  const generateSlug = (title) => {
    return title.toLowerCase().replace(/\s+/g, '-').replace(/[^\w-]+/g, '');
  };

  const handleOpenPageModal = (page = null) => {
    setCurrentPage(page);
    if (page) {
      setPageTitle(page.name);
      setPageSlug(page.slug);
      setPageContent(page.content);
      setPageStatus(page.status);
    } else {
      setPageTitle(''); setPageSlug(''); setPageContent(''); setPageStatus('Published');
    }
    setIsPageModalOpen(true);
  };
  
  const handleOpenFaqModal = (faqItem = null) => {
    setCurrentFaqItem(faqItem);
    if (faqItem) {
      setFaqQuestionId(faqItem.question_id || '');
      setFaqAnswerId(faqItem.answer_id || '');
      setFaqQuestionEn(faqItem.question_en || '');
      setFaqAnswerEn(faqItem.answer_en || '');
      setFaqQuestionAr(faqItem.question_ar || '');
      setFaqAnswerAr(faqItem.answer_ar || '');
    } else {
      setFaqQuestionId(''); setFaqAnswerId('');
      setFaqQuestionEn(''); setFaqAnswerEn('');
      setFaqQuestionAr(''); setFaqAnswerAr('');
    }
    setIsFaqModalOpen(true);
  };

  const handleSavePage = () => {
    if (!pageTitle || !pageSlug) {
      toast({ title: "Judul dan Slug Wajib Diisi", variant: "destructive" });
      return;
    }
    const pageData = { name: pageTitle, slug: pageSlug, content: pageContent, status: pageStatus, lastUpdated: new Date().toISOString().split('T')[0] };
    const updatedPages = currentPage ? pages.map(p => p.id === currentPage.id ? { ...p, ...pageData } : p) : [...pages, { id: generateSlug(pageTitle) + Date.now(), ...pageData }];
    updatePages(updatedPages);
    setIsPageModalOpen(false); setCurrentPage(null);
  };

  const handleSaveFaqItem = () => {
    const baseQuestion = faqQuestionId || faqQuestionEn || faqQuestionAr;
    const baseAnswer = faqAnswerId || faqAnswerEn || faqAnswerAr;

    if (!baseQuestion || !baseAnswer ) {
        toast({ title: "Pertanyaan dan Jawaban Wajib Diisi (minimal satu bahasa)", variant: "destructive" });
        return;
    }
    const faqData = { 
        question_id: faqQuestionId, answer_id: faqAnswerId,
        question_en: faqQuestionEn, answer_en: faqAnswerEn,
        question_ar: faqQuestionAr, answer_ar: faqAnswerAr,
        question: baseQuestion,
        answer: baseAnswer,
    };
    const updatedFaqItems = currentFaqItem ? faqItems.map(item => item.id === currentFaqItem.id ? { ...item, ...faqData } : item) : [...faqItems, { id: 'faq' + Date.now(), ...faqData }];
    updateFaqItems(updatedFaqItems);
    setIsFaqModalOpen(false); setCurrentFaqItem(null);
  };

  const handleDeletePage = (pageId) => {
    const updatedPages = pages.filter(p => p.id !== pageId);
    updatePages(updatedPages);
  };
  
  const handleDeleteFaqItem = (faqId) => {
    const updatedFaqItems = faqItems.filter(item => item.id !== faqId);
    updateFaqItems(updatedFaqItems);
  };
  
  const handleViewPage = (slug) => {
    if (slug === 'faq') {
        window.open('/faq', '_blank');
        return;
    }
    window.open(`/${slug}`, '_blank');
  };

  const containerVariants = { hidden: { opacity: 0 }, visible: { opacity: 1, transition: { staggerChildren: 0.1 } }};
  const itemVariants = { hidden: { opacity: 0, y: 20 }, visible: { opacity: 1, y: 0, transition: { duration: 0.3 } }};

  if (isLoading) {
    return <div className="text-center text-white">Memuat konten...</div>;
  }

  return (
    <div className="space-y-8">
      <motion.div initial={{ opacity: 0, y: -20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }}>
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4">
          <h1 className="text-3xl font-bold text-white tracking-tight">Manajemen Halaman Statis</h1>
          <Button onClick={() => handleOpenPageModal()} className="gold-gradient text-black font-semibold hover:opacity-90">
            <PlusCircle size={20} className="mr-2" /> Tambah Halaman Statis
          </Button>
        </div>
        <motion.div variants={containerVariants} initial="hidden" animate="visible" className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {pages.map((page) => (
            <motion.div key={page.id} variants={itemVariants}>
              <Card className="bg-gray-800 border-gray-700 text-white shadow-lg hover:shadow-primary/30 transition-shadow duration-300 flex flex-col h-full">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                  <CardTitle className="text-lg font-semibold text-primary truncate" title={page.name}>{page.name}</CardTitle>
                  <FileText className="h-6 w-6 text-gray-400" />
                </CardHeader>
                <CardContent className="space-y-2 flex-grow">
                  <p className="text-sm text-gray-300">Slug: /{page.slug}</p>
                  <p className="text-sm text-gray-300">Terakhir diupdate: {page.lastUpdated}</p>
                  <p className={`text-sm font-medium ${page.status === 'Published' ? 'text-green-400' : 'text-yellow-400'}`}>Status: {page.status}</p>
                </CardContent>
                <CardFooter className="flex justify-end space-x-2 border-t border-gray-600 pt-4">
                  <Button variant="ghost" size="sm" onClick={() => handleViewPage(page.slug)} className="text-gray-300 hover:bg-gray-700 hover:text-white"><Eye size={16} className="mr-1.5" /> Lihat</Button>
                  <Button variant="outline" size="sm" onClick={() => handleOpenPageModal(page)} className="text-primary border-primary hover:bg-primary/10 hover:text-primary"><Edit size={16} className="mr-1.5" /> Edit</Button>
                  <Button variant="destructiveOutline" size="sm" onClick={() => handleDeletePage(page.id)} className="text-red-400 border-red-400 hover:bg-red-400/10 hover:text-red-400"><Trash2 size={16} className="mr-1.5" /> Hapus</Button>
                </CardFooter>
              </Card>
            </motion.div>
          ))}
        </motion.div>
      </motion.div>

      <motion.div initial={{ opacity: 0, y: -20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5, delay: 0.2 }}>
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 my-6 pt-6 border-t border-gray-700">
          <h2 className="text-2xl font-bold text-white tracking-tight">Manajemen Pertanyaan Umum (FAQ)</h2>
          <Button onClick={() => handleOpenFaqModal()} className="gold-gradient text-black font-semibold hover:opacity-90">
            <ListPlus size={20} className="mr-2" /> Tambah Item FAQ
          </Button>
        </div>
        <motion.div variants={containerVariants} initial="hidden" animate="visible" className="space-y-4">
          {faqItems.map((item) => (
            <motion.div key={item.id} variants={itemVariants}>
              <Card className="bg-gray-800 border-gray-700 text-white shadow-md hover:shadow-primary/20 transition-shadow duration-300">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-md font-semibold text-primary truncate" title={item.question_id || item.question_en || item.question_ar || item.question}>{item.question_id || item.question_en || item.question_ar || item.question}</CardTitle>
                  <HelpCircle className="h-5 w-5 text-gray-400" />
                </CardHeader>
                <CardContent className="text-sm text-gray-300 line-clamp-2 pb-3" dangerouslySetInnerHTML={{ __html: item.answer_id || item.answer_en || item.answer_ar || item.answer || '' }} />
                <CardFooter className="flex justify-end space-x-2 border-t border-gray-600 pt-3">
                  <Button variant="outline" size="sm" onClick={() => handleOpenFaqModal(item)} className="text-primary border-primary hover:bg-primary/10 hover:text-primary"><Edit size={14} className="mr-1" /> Edit</Button>
                  <Button variant="destructiveOutline" size="sm" onClick={() => handleDeleteFaqItem(item.id)} className="text-red-400 border-red-400 hover:bg-red-400/10 hover:text-red-400"><Trash2 size={14} className="mr-1" /> Hapus</Button>
                </CardFooter>
              </Card>
            </motion.div>
          ))}
           {faqItems.length === 0 && <p className="text-center text-gray-400 py-4">Belum ada item FAQ. Klik "Tambah Item FAQ" untuk memulai.</p>}
        </motion.div>
      </motion.div>

      <Dialog open={isPageModalOpen} onOpenChange={setIsPageModalOpen}>
        <DialogContent className="bg-gray-800 border-gray-700 text-white sm:max-w-[700px]">
          <DialogHeader><DialogTitle className="text-primary text-xl">{currentPage ? 'Edit Halaman' : 'Tambah Halaman Statis'}</DialogTitle></DialogHeader>
          <div className="space-y-4 py-4 max-h-[70vh] overflow-y-auto pr-2">
            <div><Label htmlFor="pageTitleModal">Judul Halaman</Label><Input id="pageTitleModal" value={pageTitle} onChange={(e) => { setPageTitle(e.target.value); if (!currentPage) setPageSlug(generateSlug(e.target.value)); }} className="bg-gray-700 border-gray-600" /></div>
            <div><Label htmlFor="pageSlugModal">Slug URL</Label><Input id="pageSlugModal" value={pageSlug} onChange={(e) => setPageSlug(e.target.value)} className="bg-gray-700 border-gray-600" /></div>
            <div><Label htmlFor="pageContentModal">Konten Halaman</Label>
              <RichTextEditor value={pageContent} onChange={setPageContent} placeholder="Masukkan konten halaman di sini..." />
            </div>
            <div><Label htmlFor="pageStatusModal">Status</Label><select id="pageStatusModal" value={pageStatus} onChange={(e) => setPageStatus(e.target.value)} className="w-full bg-gray-700 border-gray-600 text-white p-2 rounded-md"><option value="Published">Published</option><option value="Draft">Draft</option></select></div>
          </div>
          <DialogPrimitiveFooter><Button variant="outline" onClick={() => setIsPageModalOpen(false)} className="text-gray-300 border-gray-600">Batal</Button><Button onClick={handleSavePage} className="gold-gradient text-black"><Save size={18} className="mr-2" /> Simpan Halaman</Button></DialogPrimitiveFooter>
        </DialogContent>
      </Dialog>
      
      <Dialog open={isFaqModalOpen} onOpenChange={setIsFaqModalOpen}>
        <DialogContent className="bg-gray-800 border-gray-700 text-white sm:max-w-[700px]">
          <DialogHeader><DialogTitle className="text-primary text-xl">{currentFaqItem ? 'Edit Item FAQ' : 'Tambah Item FAQ Baru'}</DialogTitle></DialogHeader>
          <div className="space-y-3 py-3 max-h-[70vh] overflow-y-auto pr-2 text-sm">
            <p className="text-xs text-gray-400 mb-3">Isi minimal satu pasang pertanyaan dan jawaban (ID/EN/AR).</p>
            
            <div className="p-3 border border-gray-600 rounded-md">
                <Label htmlFor="faqQuestionIdModal" className="text-gray-300 font-semibold">Pertanyaan (ID)</Label>
                <Input id="faqQuestionIdModal" value={faqQuestionId} onChange={e => setFaqQuestionId(e.target.value)} className="bg-gray-700 border-gray-600 mt-1" />
                <Label htmlFor="faqAnswerIdModal" className="text-gray-300 font-semibold mt-2 block">Jawaban (ID)</Label>
                <RichTextEditor value={faqAnswerId} onChange={setFaqAnswerId} placeholder="Jawaban dalam Bahasa Indonesia..." />
            </div>

            <div className="p-3 border border-gray-600 rounded-md">
                <Label htmlFor="faqQuestionEnModal" className="text-gray-300 font-semibold">Pertanyaan (EN)</Label>
                <Input id="faqQuestionEnModal" value={faqQuestionEn} onChange={e => setFaqQuestionEn(e.target.value)} className="bg-gray-700 border-gray-600 mt-1" />
                <Label htmlFor="faqAnswerEnModal" className="text-gray-300 font-semibold mt-2 block">Jawaban (EN)</Label>
                <RichTextEditor value={faqAnswerEn} onChange={setFaqAnswerEn} placeholder="Answer in English..." />
            </div>

            <div className="p-3 border border-gray-600 rounded-md">
                <Label htmlFor="faqQuestionArModal" className="text-gray-300 font-semibold">Pertanyaan (AR)</Label>
                <Input id="faqQuestionArModal" value={faqQuestionAr} onChange={e => setFaqQuestionAr(e.target.value)} className="bg-gray-700 border-gray-600 mt-1" dir="rtl" />
                <Label htmlFor="faqAnswerArModal" className="text-gray-300 font-semibold mt-2 block">Jawaban (AR)</Label>
                <RichTextEditor value={faqAnswerAr} onChange={setFaqAnswerAr} placeholder="الإجابة باللغة العربية..." />
            </div>

          </div>
          <DialogPrimitiveFooter><Button variant="outline" onClick={() => setIsFaqModalOpen(false)} className="text-gray-300 border-gray-600">Batal</Button><Button onClick={handleSaveFaqItem} className="gold-gradient text-black"><Save size={18} className="mr-2" /> Simpan Item FAQ</Button></DialogPrimitiveFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AdminContentPage;
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card.jsx';
import { Button } from '@/components/ui/button.jsx';
import { Input } from '@/components/ui/input.jsx';
import { Label } from '@/components/ui/label.jsx';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select.jsx';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs.jsx";
import { Send, BarC<PERSON>, Clock, Settings, Trash2, Edit2, PlusCircle, Save } from 'lucide-react';
import { motion } from 'framer-motion';
import { useToast } from '@/components/ui/use-toast.js';
import ContactManagementTab from '@/components/admin/marketing_booster/ContactManagementTab.jsx';
import MessageEditorTab from '@/components/admin/marketing_booster/MessageEditorTab.jsx';
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle as DialogPrimitiveTitle, DialogFooter as DialogPrimitiveFooter } from "@/components/ui/dialog.jsx";
import RichTextEditor from '@/components/admin/RichTextEditor.jsx';


const BoosterSettingsTab = ({ senders, setSenders }) => {
  const { toast } = useToast();
  const [isSenderModalOpen, setIsSenderModalOpen] = useState(false);
  const [currentSender, setCurrentSender] = useState(null);
  const [senderType, setSenderType] = useState('email');
  const [senderIdentifier, setSenderIdentifier] = useState('');
  const [senderLabel, setSenderLabel] = useState('');

  const handleOpenSenderModal = (sender = null) => {
    setCurrentSender(sender);
    if (sender) {
      setSenderType(sender.type);
      setSenderIdentifier(sender.identifier);
      setSenderLabel(sender.label);
    } else {
      setSenderType('email');
      setSenderIdentifier('');
      setSenderLabel('');
    }
    setIsSenderModalOpen(true);
  };

  const handleSaveSender = () => {
    if (!senderIdentifier || !senderLabel) {
      toast({ title: "Data Tidak Lengkap", description: "Label dan Email/Nomor WA wajib diisi.", variant: "destructive" });
      return;
    }
    const newSender = {
      id: currentSender ? currentSender.id : Date.now().toString(),
      type: senderType,
      identifier: senderIdentifier,
      label: senderLabel
    };
    const updatedSenders = currentSender ? senders.map(s => s.id === currentSender.id ? newSender : s) : [...senders, newSender];
    setSenders(updatedSenders);
    localStorage.setItem('marketingSenders', JSON.stringify(updatedSenders));
    toast({ title: `Pengirim ${currentSender ? 'Diperbarui' : 'Ditambahkan'}` });
    setIsSenderModalOpen(false);
  };

  const handleDeleteSender = (senderId) => {
    const updatedSenders = senders.filter(s => s.id !== senderId);
    setSenders(updatedSenders);
    localStorage.setItem('marketingSenders', JSON.stringify(updatedSenders));
    toast({ title: "Pengirim Dihapus" });
  };
  
  const emailSenders = senders.filter(s => s.type === 'email');
  const whatsappSenders = senders.filter(s => s.type === 'whatsapp');

  return (
    <Card className="bg-gray-800 border-gray-700">
      <CardHeader>
        <CardTitle className="text-xl text-primary">Pengaturan Kontak Pengirim</CardTitle>
        <p className="text-sm text-gray-400">Kelola daftar email dan nomor WhatsApp yang akan digunakan untuk mengirim kampanye.</p>
        <Button onClick={() => handleOpenSenderModal()} className="mt-2 w-fit"><PlusCircle size={18} className="mr-2"/> Tambah Pengirim Baru</Button>
      </CardHeader>
      <CardContent className="space-y-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-200 mb-2">Pengirim Email</h3>
          {emailSenders.length === 0 && <p className="text-gray-500">Belum ada pengirim email ditambahkan.</p>}
          <ul className="space-y-2">
            {emailSenders.map(sender => (
              <li key={sender.id} className="flex justify-between items-center p-3 bg-gray-700/50 rounded-md">
                <div>
                  <p className="font-medium">{sender.label}</p>
                  <p className="text-sm text-gray-400">{sender.identifier}</p>
                </div>
                <div>
                  <Button variant="ghost" size="icon" onClick={() => handleOpenSenderModal(sender)} className="hover:text-primary"><Edit2 size={16} /></Button>
                  <Button variant="ghost" size="icon" onClick={() => handleDeleteSender(sender.id)} className="hover:text-red-500"><Trash2 size={16} /></Button>
                </div>
              </li>
            ))}
          </ul>
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-200 mb-2">Pengirim WhatsApp</h3>
           {whatsappSenders.length === 0 && <p className="text-gray-500">Belum ada pengirim WhatsApp ditambahkan.</p>}
          <ul className="space-y-2">
            {whatsappSenders.map(sender => (
              <li key={sender.id} className="flex justify-between items-center p-3 bg-gray-700/50 rounded-md">
                <div>
                  <p className="font-medium">{sender.label}</p>
                  <p className="text-sm text-gray-400">{sender.identifier}</p>
                </div>
                <div>
                  <Button variant="ghost" size="icon" onClick={() => handleOpenSenderModal(sender)} className="hover:text-primary"><Edit2 size={16} /></Button>
                  <Button variant="ghost" size="icon" onClick={() => handleDeleteSender(sender.id)} className="hover:text-red-500"><Trash2 size={16} /></Button>
                </div>
              </li>
            ))}
          </ul>
        </div>
        <p className="text-xs text-gray-500 mt-4">Untuk pengiriman aktual, integrasi dengan layanan SMTP (untuk email) atau API WhatsApp Business (misalnya Gupshup, Twilio) diperlukan di sisi backend.</p>
      </CardContent>
      <Dialog open={isSenderModalOpen} onOpenChange={setIsSenderModalOpen}>
        <DialogContent className="bg-gray-800 border-gray-700 text-white">
          <DialogHeader><DialogPrimitiveTitle className="text-primary">{currentSender ? 'Edit Pengirim' : 'Tambah Pengirim Baru'}</DialogPrimitiveTitle></DialogHeader>
          <div className="space-y-3 py-2">
            <div><Label htmlFor="senderTypeModal">Jenis Pengirim</Label>
              <Select value={senderType} onValueChange={setSenderType}>
                <SelectTrigger className="bg-gray-700 border-gray-600"><SelectValue /></SelectTrigger>
                <SelectContent className="bg-gray-800 text-white border-gray-700">
                  <SelectItem value="email" className="hover:bg-primary/20">Email</SelectItem>
                  <SelectItem value="whatsapp" className="hover:bg-primary/20">WhatsApp</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div><Label htmlFor="senderLabelModal">Label Pengirim (Nama Tampilan)</Label><Input id="senderLabelModal" value={senderLabel} onChange={e => setSenderLabel(e.target.value)} className="bg-gray-700 border-gray-600" /></div>
            <div><Label htmlFor="senderIdentifierModal">{senderType === 'email' ? 'Alamat Email' : 'Nomor WhatsApp (dengan kode negara, cth: 62812...)'}</Label><Input id="senderIdentifierModal" value={senderIdentifier} onChange={e => setSenderIdentifier(e.target.value)} className="bg-gray-700 border-gray-600" /></div>
          </div>
          <DialogPrimitiveFooter>
            <Button variant="outline" onClick={() => setIsSenderModalOpen(false)} className="text-gray-300 border-gray-600">Batal</Button>
            <Button onClick={handleSaveSender} className="gold-gradient text-black"><Save size={16} className="mr-2"/> Simpan Pengirim</Button>
          </DialogPrimitiveFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
};


const AdminMarketingBoosterPage = () => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("contacts");

  const [campaignMode, setCampaignMode] = useState('email');
  const [messageTitle, setMessageTitle] = useState('');
  const [messageContent, setMessageContent] = useState('');
  const [templates, setTemplates] = useState([]);
  const [selectedTemplate, setSelectedTemplate] = useState('');
  
  const [senders, setSenders] = useState([]);
  const [selectedSenderIdentifier, setSelectedSenderIdentifier] = useState('');
  const [scheduleDateTime, setScheduleDateTime] = useState('');

  const initialTemplates = [
    { name: 'Promo Umrah Awal Tahun', title: '🌟 Spesial Promo Umrah Awal Tahun!', content: '<p>Assalamualaikum {nama},</p><p>Dapatkan diskon spesial untuk paket umrah awal tahun kami. Info lebih lanjut di link berikut: [LINK_PROMO]</p><p><br></p><p>Salam,</p><p>Tim Umrahservice.co</p>' },
    { name: 'Update Layanan Handling', title: 'Informasi Terbaru Layanan Handling', content: '<p>Yth. {nama},</p><p>Ada update terbaru mengenai layanan handling kami yang berlaku mulai {tanggal}. Detailnya bisa Anda lihat di [LINK_UPDATE].</p><p><br></p><p>Terima kasih,</p><p>Tim Umrahservice.co</p>' },
  ];

  useEffect(() => {
    const storedTemplates = localStorage.getItem('messageTemplates');
    setTemplates(storedTemplates ? JSON.parse(storedTemplates) : initialTemplates);

    const storedSenders = localStorage.getItem('marketingSenders');
    setSenders(storedSenders ? JSON.parse(storedSenders) : [
      { id: 'default_email_1', type: 'email', identifier: '<EMAIL>', label: 'Email Info Resmi' },
      { id: 'default_wa_1', type: 'whatsapp', identifier: '+6281234567890', label: 'WA CS Utama' },
    ]);
  }, []);
  
  useEffect(() => {
    const availableSenders = senders.filter(s => s.type === campaignMode);
    if (availableSenders.length > 0) {
      setSelectedSenderIdentifier(availableSenders[0].identifier);
    } else {
      setSelectedSenderIdentifier('');
    }
  }, [campaignMode, senders]);

  const handleSendMessage = () => {
    if (!selectedSenderIdentifier) {
      toast({ title: "Pengirim Belum Dipilih", description: `Pilih pengirim ${campaignMode} terlebih dahulu dari daftar di Pengaturan Booster.`, variant: "destructive"});
      return;
    }
    if (!messageContent) {
        toast({ title: "Pesan Kosong", description: "Isi pesan tidak boleh kosong.", variant: "destructive"});
        return;
    }
    if (campaignMode === 'email' && !messageTitle) {
        toast({ title: "Subjek Email Kosong", description: "Subjek email tidak boleh kosong.", variant: "destructive"});
        return;
    }

    toast({
      title: "Simulasi Pengiriman Pesan",
      description: `Pesan ${campaignMode} akan dikirim dari "${selectedSenderIdentifier}" dengan judul "${messageTitle || '(tidak ada subjek untuk WA)'}". Ini adalah simulasi, integrasi API diperlukan untuk pengiriman aktual.`,
      duration: 7000
    });
    console.log({
        mode: campaignMode,
        sender: selectedSenderIdentifier,
        title: messageTitle,
        content: messageContent,
        schedule: scheduleDateTime || 'Kirim Sekarang',
        target: 'Semua Kontak (Simulasi)'
    });
  };

  return (
    <div className="space-y-6">
      <motion.div initial={{ opacity: 0, y: -20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }} className="flex items-center gap-4">
        <Send size={32} className="text-primary" />
        <h1 className="text-3xl font-bold text-white tracking-tight">Email & WhatsApp Marketing Booster</h1>
      </motion.div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2 md:grid-cols-5 bg-gray-700 text-gray-300">
          <TabsTrigger value="contacts" className="data-[state=active]:bg-primary data-[state=active]:text-black">Kelola Kontak</TabsTrigger>
          <TabsTrigger value="editor" className="data-[state=active]:bg-primary data-[state=active]:text-black">Editor Pesan</TabsTrigger>
          <TabsTrigger value="sender" className="data-[state=active]:bg-primary data-[state=active]:text-black">Preview & Kirim</TabsTrigger>
          <TabsTrigger value="stats" className="data-[state=active]:bg-primary data-[state=active]:text-black">Statistik</TabsTrigger>
          <TabsTrigger value="settings" className="data-[state=active]:bg-primary data-[state=active]:text-black"><Settings size={16} className="mr-1 hidden sm:inline-block"/>Pengaturan</TabsTrigger>
        </TabsList>

        <TabsContent value="contacts" className="mt-4">
          <ContactManagementTab />
        </TabsContent>

        <TabsContent value="editor" className="mt-4">
          <MessageEditorTab 
            campaignMode={campaignMode}
            setCampaignMode={setCampaignMode}
            messageTitle={messageTitle}
            setMessageTitle={setMessageTitle}
            messageContent={messageContent}
            setMessageContent={setMessageContent}
            templates={templates}
            setTemplates={setTemplates}
            selectedTemplate={selectedTemplate}
            setSelectedTemplate={setSelectedTemplate}
            RichTextEditorComponent={RichTextEditor}
          />
        </TabsContent>

        <TabsContent value="sender" className="mt-4">
          <Card className="bg-gray-800 border-gray-700">
            <CardHeader><CardTitle className="text-xl text-primary">Preview Pesan & Opsi Pengiriman</CardTitle></CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label>Preview Pesan ({campaignMode === 'email' ? 'Email' : 'WhatsApp'})</Label>
                <div className="p-4 border border-gray-600 rounded-md bg-gray-700/50 min-h-[150px] prose prose-sm prose-invert max-w-none"
                  dangerouslySetInnerHTML={{ __html: campaignMode === 'email' ? `<strong>Subjek: ${messageTitle || '(Belum ada subjek)'}</strong><br/><br/>${messageContent || '(Belum ada isi pesan untuk ditampilkan.)'}` : messageContent || '(Belum ada isi pesan untuk ditampilkan.)' }}
                />
                <p className="text-xs text-gray-500 mt-1">Variabel seperti {`{nama}`} akan diganti saat pengiriman aktual.</p>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div><Label>Pilih Pengirim {campaignMode === 'email' ? 'Email' : 'WA'}</Label>
                <Select value={selectedSenderIdentifier} onValueChange={setSelectedSenderIdentifier}>
                    <SelectTrigger className="bg-gray-700 border-gray-600"><SelectValue placeholder="Pilih pengirim..." /></SelectTrigger>
                    <SelectContent className="bg-gray-800 text-white border-gray-700">
                        {senders.filter(s => s.type === campaignMode).map(sender => (
                          <SelectItem key={sender.id} value={sender.identifier} className="hover:bg-primary/20">{sender.label} ({sender.identifier})</SelectItem>
                        ))}
                        {senders.filter(s => s.type === campaignMode).length === 0 && <p className="p-2 text-sm text-gray-400">Belum ada pengirim {campaignMode}. Tambahkan di Pengaturan.</p>}
                    </SelectContent>
                </Select>
                </div>
                <div><Label htmlFor="scheduleSend" className="flex items-center"><Clock size={16} className="mr-1"/> Jadwalkan Pengiriman (Opsional)</Label><Input id="scheduleSend" type="datetime-local" value={scheduleDateTime} onChange={e => setScheduleDateTime(e.target.value)} className="bg-gray-700 border-gray-600" /></div>
              </div>
              <div><Label>Kirim Ke:</Label>
                <Select defaultValue="all">
                    <SelectTrigger className="bg-gray-700 border-gray-600"><SelectValue /></SelectTrigger>
                    <SelectContent className="bg-gray-800 text-white border-gray-700">
                        <SelectItem value="all" className="hover:bg-primary/20">Semua Kontak (dari tab Kelola Kontak)</SelectItem>
                        <SelectItem value="selected" className="hover:bg-primary/20" disabled>Kontak Terpilih (Fitur akan datang)</SelectItem>
                        <SelectItem value="filtered" className="hover:bg-primary/20" disabled>Berdasarkan Filter (Fitur akan datang)</SelectItem>
                    </SelectContent>
                </Select>
              </div>
              <Button onClick={handleSendMessage} className="w-full gold-gradient text-black font-semibold mt-4 text-lg py-3"><Send size={20} className="mr-2" /> Kirim Sekarang (Simulasi)</Button>
              <p className="text-xs text-gray-500 mt-1 text-center">Untuk WhatsApp, proses kirim bertahap & auto-delay akan dipertimbangkan saat implementasi backend.</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="stats" className="mt-4">
          <Card className="bg-gray-800 border-gray-700">
            <CardHeader><CardTitle className="text-xl text-primary">Statistik & Pelaporan Kampanye</CardTitle></CardHeader>
            <CardContent className="text-center py-10">
              <BarChart size={48} className="mx-auto text-gray-500 mb-4" />
              <p className="text-gray-400">Laporan jumlah pesan terkirim, gagal, dibuka (untuk email), dan status real-time akan ditampilkan di sini setelah integrasi backend.</p>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="settings" className="mt-4">
          <BoosterSettingsTab senders={senders} setSenders={setSenders} />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AdminMarketingBoosterPage;
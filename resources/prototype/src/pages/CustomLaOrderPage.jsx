import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useToast } from "@/components/ui/use-toast";
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import SEO from '@/components/shared/SEO';
import { Button } from "@/components/ui/button";
import { Send, Package, HeartHandshake as Handshake, ShieldCheck, Layers, ArrowLeft } from 'lucide-react';
import { addDays } from 'date-fns';
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { useNavigate, useLocation } from 'react-router-dom';
import { orderStorageService } from '@/services/orderStorageService';
import { v4 as uuidv4 } from 'uuid';

import TravelInfoSection from '@/components/custom_la/TravelInfoSection';
import FlightInfoSection from '@/components/custom_la/FlightInfoSection';
import RoomCompositionSection from '@/components/custom_la/RoomCompositionSection';
import HotelInputSection from '@/components/custom_la/HotelInputSection';
import HandlingPackageSection from '@/components/custom_la/HandlingPackageSection';
import AdditionalProgramsSection from '@/components/custom_la/AdditionalProgramsSection';
import MutawwifRequestSection from '@/components/custom_la/MutawwifRequestSection';
import AdditionalServicesSection from '@/components/custom_la/AdditionalServicesSection';
import DocumentUploadSection from '@/components/custom_la/DocumentUploadSection';
import NotesSection from '@/components/custom_la/NotesSection';
import SummarySidebar from '@/components/custom_la/SummarySidebar';

const initialFormData = {
    serviceType: 'la_b2b',
    travelInfo: { travelName: '', picName: '', whatsapp: '', email: '', jamaahOrigin: '' },
    flightInfo: { departureDate: null, returnDate: null, ticketStatus: 'Sudah Ada', departureAirline: '', departureFlightNumber: '', returnAirline: '', returnFlightNumber: '' },
    roomComposition: { quad: '', triple: '', double: '', single: '' },
    hotels: [
        { id: uuidv4(), hotelName: '', city: 'Makkah', checkIn: null, nights: '', checkOut: null }
    ],
    handlingPackage: { type: 'Basic' },
    additionalPrograms: {
        thaifTour: { enabled: false, charterBus: true, cableCarTicket: false, toboganRide: false, lunch: false },
        haramainTrain: { routes: { makkahToMadinah: false, madinahToMakkah: false, madinahToJeddah: false, jeddahToMadinah: false }, paxCount: '', timePreference: '' }
    },
    mutawwifRequest: {
        needed: 'Tidak',
        style: { sunnah: false, communicative: false, calm: false, memorizer: false, knowledgeable: false },
        skills: { photoVideo: false, mc: false, manasik: false },
        languages: { indonesia: true, arabic: false, english: false },
        notes: ''
    },
    additionalServices: {
        extraZiarah: { enabled: false, location: '' },
        extraMeal: { enabled: false, details: '' },
        proDocumentation: false
    },
    documents: { roomlist: null, ticket: null, visa: null, manifest: null },
    notes: ''
};

const serviceTypeDetails = {
    la_b2b: {
        title: "Formulir Custom: Paket LA B2B",
        description: "Rancang paket Land Arrangement B2B Anda dengan presisi. Isi semua detail untuk mendapatkan penawaran terbaik.",
        icon: <Package className="mr-3 h-5 w-5 text-amber-400" />,
        label: "Paket LA B2B",
        serviceDescription: "Paket lengkap Land Arrangement, sudah termasuk visa, hotel, dan handling."
    },
    handling_only: {
        title: "Formulir Custom: Paket Handling Service",
        description: "Lengkapi detail untuk layanan handling saja di Arab Saudi. Fokus pada pelayanan di lapangan.",
        icon: <Handshake className="mr-3 h-5 w-5 text-sky-400" />,
        label: "Paket Handling Service",
        serviceDescription: "Hanya layanan handling di Arab Saudi tanpa visa dan hotel."
    },
    bundling_visa: {
        title: "Formulir Custom: Paket Handling + Visa",
        description: "Rancang paket bundling Anda yang mencakup layanan handling dan pengurusan visa.",
        icon: <ShieldCheck className="mr-3 h-5 w-5 text-green-400" />,
        label: "Paket Bundling Handling + Visa",
        serviceDescription: "Paket layanan handling dengan pengurusan visa, tanpa booking hotel."
    }
};

const ServiceTypeOption = ({ id, value, checked }) => {
    const details = serviceTypeDetails[value];
    return (
        <Label
            htmlFor={id}
            className={`flex items-start p-4 border rounded-lg cursor-pointer transition-all duration-300 ${
                checked ? 'border-amber-400 bg-amber-500/10 shadow-md shadow-amber-500/10' : 'border-gray-700 bg-gray-900/50 hover:border-amber-400/50'
            }`}
        >
            <RadioGroupItem value={value} id={id} className="mt-1" />
            <div className="flex-grow ml-4">
                <span className="text-lg font-bold text-white flex items-center">
                    {details.icon}
                    {details.label}
                </span>
                <p className="text-gray-400 mt-1">{details.serviceDescription}</p>
            </div>
        </Label>
    );
};


const CustomLaOrderPage = () => {
    const { toast } = useToast();
    const navigate = useNavigate();
    const location = useLocation();
    const [formData, setFormData] = useState(initialFormData);

    useEffect(() => {
        if(location.state?.formData) {
            setFormData(prev => ({
                ...prev,
                travelInfo: location.state.formData.bookerInfo
            }));
        }
    }, [location.state]);


    const handleFormChange = useCallback((section, field, value) => {
        setFormData(prev => {
            const newSectionData = field ? { ...prev[section], [field]: value } : value;
            const newFormData = { ...prev, [section]: newSectionData };

            if (section === 'flightInfo' && field === 'departureDate' && newFormData.hotels.length > 0) {
                newFormData.hotels[0].checkIn = value;
            }
            
            return newFormData;
        });
    }, []);

    const handleNestedChange = useCallback((section, field, subField, value) => {
        setFormData(prev => ({
            ...prev,
            [section]: {
                ...prev[section],
                [field]: {
                    ...prev[section][field],
                    [subField]: value
                }
            }
        }));
    }, []);
    
    const handleThaifChange = useCallback((field, value) => {
        setFormData(prev => ({
            ...prev,
            additionalPrograms: {
                ...prev.additionalPrograms,
                thaifTour: {
                    ...prev.additionalPrograms.thaifTour,
                    [field]: value
                }
            }
        }));
    }, []);

    const handleSingleFieldChange = useCallback((field, value) => {
         setFormData(prev => ({ ...prev, [field]: value }));
    }, []);
    
    const handleServiceTypeChange = (value) => {
      handleSingleFieldChange('serviceType', value);
    };

    const handleDateChange = useCallback((field, date) => {
        handleFormChange('flightInfo', field, date);
    }, [handleFormChange]);

    const handleHotelChange = useCallback((id, field, value) => {
        setFormData(prev => {
            const newHotels = [...prev.hotels];
            const hotelIndex = newHotels.findIndex(h => h.id === id);

            if (hotelIndex !== -1) {
                const updatedHotel = { ...newHotels[hotelIndex], [field]: value };

                if (field === 'checkIn' || field === 'nights') {
                    if (updatedHotel.checkIn && updatedHotel.nights) {
                        updatedHotel.checkOut = addDays(new Date(updatedHotel.checkIn), parseInt(updatedHotel.nights, 10) || 0);
                    } else {
                        updatedHotel.checkOut = null;
                    }
                }
                newHotels[hotelIndex] = updatedHotel;

                for (let i = hotelIndex + 1; i < newHotels.length; i++) {
                    const prevHotelCheckout = newHotels[i - 1].checkOut;
                    if (prevHotelCheckout) {
                        newHotels[i].checkIn = prevHotelCheckout;
                         if (newHotels[i].nights) {
                            newHotels[i].checkOut = addDays(new Date(newHotels[i].checkIn), parseInt(newHotels[i].nights, 10) || 0);
                        }
                    }
                }
            }

            return { ...prev, hotels: newHotels };
        });
    }, []);

    const handleFileChange = useCallback((field, file) => {
        handleFormChange('documents', field, file);
    }, [handleFormChange]);
    
    const removeFile = useCallback((field) => {
        handleFormChange('documents', field, null);
    }, [handleFormChange]);

    const totalJamaah = useMemo(() => {
        const { quad, triple, double, single } = formData.roomComposition;
        return (parseInt(quad, 10) || 0) * 4 +
               (parseInt(triple, 10) || 0) * 3 +
               (parseInt(double, 10) || 0) * 2 +
               (parseInt(single, 10) || 0) * 1;
    }, [formData.roomComposition]);

    const totalNights = useMemo(() => {
        return formData.hotels.reduce((acc, hotel) => acc + (parseInt(hotel.nights, 10) || 0), 0);
    }, [formData.hotels]);

    const handleSubmit = (e) => {
        e.preventDefault();
        try {
            const summary = {
                packageName: `Custom: ${serviceTypeDetails[formData.serviceType].label}`,
                totalPax: totalJamaah,
                totalNights: totalNights,
                total: 'Akan diinfokan',
            };
            const newOrder = orderStorageService.addOrder({ packageType: 'custom_request', ...formData }, summary);
            toast({
                title: "Permintaan Penawaran Terkirim!",
                description: "Anda akan diarahkan ke halaman detail permintaan.",
            });
            navigate(`/order/success/${newOrder.id}`);
        } catch (error) {
            toast({
                title: "Gagal Mengirim Permintaan",
                description: "Terjadi kesalahan saat menyimpan permintaan Anda.",
                variant: "destructive",
            });
        }
    };
    
    const pageVariants = {
        initial: { opacity: 0, y: 20 },
        in: { opacity: 1, y: 0 },
        out: { opacity: 0, y: -20 },
    };

    const pageTransition = {
        type: "tween",
        ease: "anticipate",
        duration: 0.5,
    };

    const currentServiceDetails = serviceTypeDetails[formData.serviceType];
    
    return (
        <>
            <SEO
                title={currentServiceDetails.title}
                description={currentServiceDetails.description}
            />
            <Navbar />
            <motion.main 
                initial="initial"
                animate="in"
                exit="out"
                variants={pageVariants}
                transition={pageTransition}
                className="bg-background pt-32 pb-20"
            >
                <div className="container mx-auto px-4 md:px-6">
                    <Button type="button" variant="outline" onClick={() => navigate('/order')} className="mb-8">
                        <ArrowLeft className="mr-2 h-4 w-4" /> Kembali ke Pilihan Paket
                    </Button>

                    <Card className="bg-gradient-to-br from-gray-800/60 to-gray-900/80 border-gray-700/60 rounded-3xl shadow-2xl overflow-hidden backdrop-blur-sm mb-12">
                        <CardHeader className="text-center pb-4">
                            <Layers className="mx-auto h-12 w-12 text-amber-400" />
                            <CardTitle className="text-4xl font-bold text-white tracking-tight mt-4">{currentServiceDetails.title}</CardTitle>
                            <CardDescription className="text-lg text-gray-400 max-w-3xl mx-auto pt-2">{currentServiceDetails.description}</CardDescription>
                        </CardHeader>
                    </Card>

                    <form onSubmit={handleSubmit} className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-start">
                        <div className="lg:col-span-2 space-y-8">

                             <Card className="bg-gray-800/50 border-gray-700">
                                <CardHeader>
                                    <CardTitle className="flex items-center">
                                        <span className="bg-amber-400 text-black rounded-full h-8 w-8 flex items-center justify-center font-bold text-lg mr-4">A</span>
                                        Jenis Layanan Custom
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <RadioGroup
                                        value={formData.serviceType}
                                        onValueChange={handleServiceTypeChange}
                                        className="space-y-4"
                                    >
                                        <ServiceTypeOption
                                            id="la-b2b"
                                            value="la_b2b"
                                            checked={formData.serviceType === 'la_b2b'}
                                        />
                                        <ServiceTypeOption
                                            id="handling-only"
                                            value="handling_only"
                                            checked={formData.serviceType === 'handling_only'}
                                        />
                                        <ServiceTypeOption
                                            id="bundling-visa"
                                            value="bundling_visa"
                                            checked={formData.serviceType === 'bundling_visa'}
                                        />
                                    </RadioGroup>
                                </CardContent>
                            </Card>

                            <TravelInfoSection formData={formData.travelInfo} handleInputChange={(...args) => handleFormChange('travelInfo', ...args)} />
                            <FlightInfoSection formData={formData.flightInfo} handleInputChange={(...args) => handleFormChange('flightInfo', ...args)} handleDateChange={handleDateChange} setFormData={setFormData} />
                            <RoomCompositionSection formData={formData.roomComposition} handleInputChange={(...args) => handleFormChange('roomComposition', ...args)} totalJamaah={totalJamaah} />
                           
                            {formData.serviceType !== 'handling_only' && (
                                <HotelInputSection formData={formData.hotels} handleHotelChange={handleHotelChange} setFormData={setFormData} />
                            )}
                            
                            <HandlingPackageSection formData={formData.handlingPackage.type} handleInputChange={(value) => handleFormChange('handlingPackage', 'type', value)} />
                            
                            <AdditionalProgramsSection 
                                formData={formData.additionalPrograms} 
                                handleInputChange={(field, value) => handleFormChange('additionalPrograms', field, value)}
                                handleCheckboxChange={handleNestedChange}
                                handleThaifChange={handleThaifChange}
                            />
                            <MutawwifRequestSection 
                                formData={formData.mutawwifRequest} 
                                handleInputChange={(field, value) => handleFormChange('mutawwifRequest', field, value)}
                                handleCheckboxChange={handleNestedChange}
                            />
                            <AdditionalServicesSection 
                                formData={formData.additionalServices} 
                                handleInputChange={(field, value) => handleFormChange('additionalServices', field, value)}
                                handleCheckboxChange={(section, field, value) => handleNestedChange(section, field, value)}
                            />
                            <DocumentUploadSection formData={formData.documents} handleFileChange={handleFileChange} removeFile={removeFile} />
                            <NotesSection value={formData.notes} handleInputChange={(field, value) => handleSingleFieldChange(field, value)} />
                        </div>

                        <div className="lg:col-span-1 lg:sticky lg:top-28 flex flex-col" style={{ height: 'calc(100vh - 7rem)' }}>
                            <div className="flex-grow overflow-y-auto pr-2">
                                <SummarySidebar formData={formData} totalJamaah={totalJamaah} totalNights={totalNights} />
                            </div>
                            <div className="flex-shrink-0 pt-6">
                                <Button type="submit" size="lg" className="w-full gold-gradient text-black font-bold text-lg py-6">
                                    <Send className="mr-2 h-5 w-5"/>
                                    Kirim Permintaan Penawaran
                                </Button>
                            </div>
                        </div>
                    </form>
                </div>
            </motion.main>
            <Footer />
        </>
    );
};

export default CustomLaOrderPage;
import React, { useState, useEffect, useRef, useContext } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useToast } from "@/components/ui/use-toast";
import { orderStorageService } from '@/services/orderStorageService';
import { generateOrderWhatsAppLink } from '@/lib/whatsapp';
import SEO from '@/components/shared/SEO';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, FileDown, Mail, MessageCircle, Copy, Home } from 'lucide-react';
import { format } from 'date-fns';
import { id } from 'date-fns/locale';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { LanguageContext } from '@/contexts/LanguageContext';

const PrintableHeader = ({ orderId }) => {
    const { translations } = useContext(LanguageContext);
    const getTranslatedText = (key, fallbackText) => translations[key] || fallbackText;
    const logoUrl = localStorage.getItem('siteLogo') || "https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/e2226538273674d2415bfb7f2ef1cba1.png";

    return (
        <div className="flex justify-between items-center p-6 bg-gray-900 text-white rounded-t-lg">
            <img src={logoUrl} alt={getTranslatedText('logoAlt', 'Arrahmah Handling Service Logo')} className="h-16 w-auto" />
            <div className="text-right">
                <h2 className="text-2xl font-bold text-amber-400">DETAIL PESANAN</h2>
                <p className="text-lg font-mono text-gray-300">No. Order: {orderId}</p>
            </div>
        </div>
    );
};

const DetailItem = ({ label, value, className = '', children }) => (
    <div className={`grid grid-cols-3 gap-2 py-3 border-b border-gray-700/50 ${className}`}>
        <dt className="font-semibold text-gray-400 col-span-1">{label}</dt>
        <dd className="text-gray-100 col-span-2">{value || children || '-'}</dd>
    </div>
);

const OrderSuccessPage = () => {
    const { orderId } = useParams();
    const navigate = useNavigate();
    const { toast } = useToast();
    const [order, setOrder] = useState(null);
    const orderDetailsRef = useRef(null);

    useEffect(() => {
        const fetchedOrder = orderStorageService.getOrderById(orderId);
        if (fetchedOrder) {
            setOrder(fetchedOrder);
        } else {
            toast({
                title: "Order Tidak Ditemukan",
                description: "Tidak dapat menemukan detail untuk order ini.",
                variant: "destructive"
            });
            navigate('/order');
        }
    }, [orderId, navigate, toast]);

    const handleCopyId = () => {
        navigator.clipboard.writeText(orderId);
        toast({ title: "Order ID disalin!" });
    };

    const handleDownloadPdf = async () => {
        const content = orderDetailsRef.current;
        if (!content) return;
    
        toast({ title: "Mempersiapkan PDF...", description: "Mohon tunggu sebentar." });
    
        try {
            const canvas = await html2canvas(content, {
                scale: 2,
                backgroundColor: '#111827',
                useCORS: true,
                logging: false,
                onclone: (document) => {
                    const header = document.getElementById('printable-header');
                    if(header) header.style.display = 'block';
                }
            });
    
            const imgData = canvas.toDataURL('image/png', 1.0);
            const pdf = new jsPDF({
                orientation: 'p',
                unit: 'mm',
                format: 'a4',
                putOnlyUsedFonts: true,
                floatPrecision: 16
            });
    
            const pdfWidth = pdf.internal.pageSize.getWidth();
            const pdfHeight = pdf.internal.pageSize.getHeight();
            const imgWidth = canvas.width;
            const imgHeight = canvas.height;
            const ratio = imgWidth / imgHeight;
            const widthInPdf = pdfWidth - 20;
            const heightInPdf = widthInPdf / ratio;
    
            pdf.addImage(imgData, 'PNG', 10, 10, widthInPdf, heightInPdf);
            pdf.save(`Order-Arrahmah-${orderId}.pdf`);
            toast({ title: "PDF berhasil diunduh!", variant: "success" });
        } catch (error) {
            console.error("Error generating PDF:", error);
            toast({ title: "Gagal membuat PDF", description: "Terjadi kesalahan saat mengunduh PDF.", variant: "destructive" });
        }
    };
    

    const handleSendWhatsApp = () => {
        const whatsappLink = generateOrderWhatsAppLink(formData, summary);
        window.open(whatsappLink, '_blank');
        toast({
            title: "Siap mengirim via WhatsApp!",
            description: "Silakan kirim pesan yang telah disiapkan."
        });
    };

    const handleSendEmail = () => {
        if(!order) return;
        const { formData, summary } = order;
        const subject = `Detail Pesanan Arrahmah Handling Service - No. ${order.id}`;
        
        let body = `Detail Pesanan untuk Order No: ${order.id}\n\n`;
        body += `Paket: ${summary.packageName}\n`;
        body += `Total Jamaah: ${summary.totalPax} orang\n`;
        body += `Estimasi Total: $${summary.total?.toFixed(2)}\n\n`;
        body += `Informasi Pemesan:\n`;
        body += `Nama Travel: ${formData.bookerInfo.travelName}\n`;
        body += `PIC: ${formData.bookerInfo.picName}\n`;
        body += `WhatsApp: ${formData.bookerInfo.whatsapp}\n\n`;
        body += `Terima kasih atas pesanan Anda.`;

        const mailtoLink = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
        window.location.href = mailtoLink;
        toast({
            title: "Buka aplikasi email Anda",
            description: "Silakan periksa aplikasi email Anda untuk mengirim pesan."
        });
    };
    
    if (!order) {
        return <div className="bg-background text-white min-h-screen flex items-center justify-center">Memuat detail order...</div>;
    }

    const { formData, summary, createdAt } = order;
    const { bookerInfo, packageType } = formData;
    const isCustomRequest = packageType === 'custom_request';

    return (
        <>
            <SEO title={`Order ${orderId} Berhasil`} description="Detail pesanan Anda telah berhasil diterima." />
            <Navbar />
            <motion.main
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="bg-background pt-32 pb-20"
            >
                <div className="container mx-auto px-4 md:px-6">
                    <Card className="bg-gradient-to-br from-green-600/10 to-gray-900 border border-green-500/30 w-full max-w-4xl mx-auto text-center p-8 rounded-2xl shadow-2xl shadow-green-500/10">
                        <CheckCircle className="mx-auto h-16 w-16 text-green-400" />
                        <h1 className="text-3xl md:text-4xl font-bold text-white mt-4">{isCustomRequest ? "Permintaan Anda Terkirim!" : "Pesanan Berhasil!"}</h1>
                        <p className="text-gray-300 mt-2 text-lg">
                            {isCustomRequest 
                                ? "Terima kasih! Permintaan penawaran Anda telah kami terima. Tim kami akan segera menghubungi Anda."
                                : "Terima kasih! Pesanan Anda telah kami terima dan akan segera kami proses."
                            }
                        </p>
                        <div className="mt-4 flex items-center justify-center gap-2">
                            <span className="text-gray-400">Order ID:</span>
                            <Badge variant="secondary" className="text-lg font-mono">{orderId}</Badge>
                            <Button variant="ghost" size="icon" className="h-8 w-8" onClick={handleCopyId}>
                                <Copy className="h-4 w-4" />
                            </Button>
                        </div>
                    </Card>

                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mt-12 max-w-6xl mx-auto">
                        <div className="lg:col-span-2">
                            <div ref={orderDetailsRef} className="bg-gray-800 rounded-lg">
                                <div id="printable-header" style={{ display: 'none' }}>
                                    <PrintableHeader orderId={orderId} />
                                </div>
                                <Card className="bg-gray-800/50 border-gray-700 p-2 border-t-0 rounded-t-none">
                                    <CardHeader>
                                        <CardTitle>Detail Pesanan</CardTitle>
                                        <CardDescription>Dibuat pada {format(new Date(createdAt), "d MMMM yyyy, HH:mm", { locale: id })}</CardDescription>
                                    </CardHeader>
                                    <CardContent className="space-y-6 px-6 pb-6">
                                        <section>
                                            <h3 className="text-lg font-semibold text-amber-400 mb-2">Informasi Pemesan</h3>
                                            <DetailItem label="Nama Travel" value={bookerInfo.travelName} />
                                            <DetailItem label="Nama PIC" value={bookerInfo.picName} />
                                            <DetailItem label="WhatsApp" value={bookerInfo.whatsapp} />
                                            <DetailItem label="Email" value={bookerInfo.email} />
                                            {bookerInfo.jamaahOrigin && <DetailItem label="Asal Jamaah" value={bookerInfo.jamaahOrigin} />}
                                        </section>
                                        
                                        <section>
                                            <h3 className="text-lg font-semibold text-amber-400 mb-2">Ringkasan Paket</h3>
                                            <DetailItem label="Paket Dipilih" value={summary.packageName} />
                                            <DetailItem label="Total Jamaah" value={`${summary.totalPax} orang`} />
                                            {!isCustomRequest && <DetailItem label="Estimasi Harga/Pax" value={`$${summary.pricePerPax?.toFixed(2)}`} />}
                                        </section>

                                        {!isCustomRequest && (
                                            <section>
                                                <h3 className="text-lg font-semibold text-amber-400 mb-2">Rincian Biaya</h3>
                                                <DetailItem label="Subtotal Paket" value={`$${summary.subtotal?.toFixed(2)}`} />
                                                {summary.addons && summary.addons.length > 0 && (
                                                    <DetailItem label="Layanan Tambahan">
                                                        <ul className="space-y-1 mt-1">
                                                            {summary.addons.map((addon, i) => (
                                                                <li key={i} className="flex justify-between">
                                                                    <span>- {addon.name}</span>
                                                                    <span>${addon.cost.toFixed(2)}</span>
                                                                </li>
                                                            ))}
                                                        </ul>
                                                    </DetailItem>
                                                )}
                                                <DetailItem label="Total Estimasi" className="text-xl !py-4 font-bold text-green-400" value={`$${summary.total?.toFixed(2)}`} />
                                            </section>
                                        )}
                                    </CardContent>
                                </Card>
                            </div>
                        </div>
                        <div className="lg:sticky lg:top-28 h-fit space-y-6">
                             <Card className="bg-gray-800/50 border-gray-700">
                                <CardHeader><CardTitle>Aksi Cepat</CardTitle></CardHeader>
                                <CardContent className="space-y-3">
                                    <Button onClick={handleDownloadPdf} className="w-full" variant="outline"><FileDown className="mr-2 h-4 w-4" /> Unduh PDF</Button>
                                    <Button onClick={handleSendWhatsApp} className="w-full" variant="outline"><MessageCircle className="mr-2 h-4 w-4" /> Kirim Pesanan via WhatsApp</Button>
                                    <Button onClick={handleSendEmail} className="w-full" variant="outline"><Mail className="mr-2 h-4 w-4" /> Kirim ke Email</Button>
                                </CardContent>
                             </Card>
                             <Button onClick={() => navigate('/')} className="w-full gold-gradient text-black"><Home className="mr-2 h-4 w-4" /> Kembali ke Beranda</Button>
                        </div>
                    </div>
                </div>
            </motion.main>
            <Footer />
        </>
    );
};

export default OrderSuccessPage;
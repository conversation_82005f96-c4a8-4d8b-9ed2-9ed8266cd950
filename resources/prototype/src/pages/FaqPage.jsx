import React, { useState, useContext, useEffect, useRef } from 'react';
import Navbar from '@/components/layout/Navbar.jsx';
import Footer from '@/components/layout/Footer.jsx';
import { LanguageContext } from '@/contexts/LanguageContext.jsx';
import SEO from '@/components/shared/SEO.jsx';
import FaqHeader from '@/components/faq/FaqHeader.jsx';
import FaqSidebar from '@/components/faq/FaqSidebar.jsx';
import FaqFooter from '@/components/faq/FaqFooter.jsx';
import { faqData } from '@/components/faq/FaqData.js';
import { getCategoryTitle } from '@/components/faq/FaqTranslations.js';
import FaqAccordion from '@/components/faq/FaqCategory.jsx';

const FaqPage = () => {
  const { language, translations } = useContext(LanguageContext);
  const [activeCategory, setActiveCategory] = useState(faqData[0].category);
  const categoryRefs = useRef({});

  const pageTitle = translations.faqPageTitle || "FAQ Lengkap - Umrahservice.co";
  const pageDescription = translations.faqPageSubtitle || "Temukan jawaban lengkap atas pertanyaan yang sering diajukan mengenai layanan Land Arrangement, Handling Umrah, visa, dan kerjasama dengan Arrahmah Handling Service.";

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setActiveCategory(entry.target.id);
          }
        });
      },
      { rootMargin: "-40% 0px -60% 0px" }
    );

    const currentRefs = categoryRefs.current;
    const refsArray = Object.values(currentRefs);
    refsArray.forEach((ref) => {
      if (ref) observer.observe(ref);
    });

    return () => {
      refsArray.forEach((ref) => {
        if (ref) observer.unobserve(ref);
      });
    };
  }, []);

  const FaqMobileNav = () => (
    <div className="w-full lg:hidden mb-8">
      <select
        onChange={(e) => {
          const element = document.getElementById(e.target.value);
          if (element) {
            element.scrollIntoView({ behavior: 'smooth', block: 'start' });
          }
        }}
        value={activeCategory}
        className="w-full px-4 py-3 rounded-md bg-gray-800 border border-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-amber-500"
      >
        {faqData.map(category => (
          <option key={category.category} value={category.category}>
            {getCategoryTitle(category.category, language)}
          </option>
        ))}
      </select>
    </div>
  );

  return (
    <>
      <SEO
        title={pageTitle}
        description={pageDescription}
      />
      <div className="min-h-screen flex flex-col bg-background text-white">
        <Navbar />
        <main className="flex-grow pt-28 pb-24 bg-gradient-to-b from-background to-secondary">
          <div className="container mx-auto px-4 md:px-6">
            <FaqHeader language={language} />

            <div className="flex flex-col lg:flex-row gap-12">
              <FaqSidebar
                categories={faqData}
                activeCategory={activeCategory}
                language={language}
              />
              
              <div className="w-full lg:w-3/4 lg:pl-10">
                <FaqMobileNav />
                <div className="space-y-16">
                  {faqData.map(category => (
                    <section 
                      key={category.category} 
                      id={category.category}
                      ref={el => (categoryRefs.current[category.category] = el)}
                      className="scroll-mt-28"
                    >
                      <h2 className="text-3xl font-bold text-white mb-8 border-l-4 border-amber-400 pl-4">
                        {getCategoryTitle(category.category, language)}
                      </h2>
                      <FaqAccordion items={category.items} language={language} />
                    </section>
                  ))}
                </div>
              </div>
            </div>
            
            <FaqFooter language={language} />
          </div>
        </main>
        <Footer />
      </div>
    </>
  );
};

export default FaqPage;
import React, { useContext } from 'react';
import Navbar from '@/components/layout/Navbar.jsx';
import Footer from '@/components/layout/Footer.jsx';
import ContactForm from '@/components/contact/ContactForm.jsx';
import ContactInfo from '@/components/contact/ContactInfo.jsx';
import ContactMap from '@/components/contact/ContactMap.jsx';
import SEO from '@/components/shared/SEO.jsx';
import { motion } from 'framer-motion';
import { Mail, CalendarPlus } from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs.jsx";
import AppointmentForm from '@/components/contact/AppointmentForm.jsx';
import { LanguageContext } from '@/contexts/LanguageContext';

const ContactPage = () => {
  const { translations } = useContext(LanguageContext);
  const getTranslation = (key, fallback) => translations[key] || fallback;

  const pageTitle = getTranslation('contact', 'Hubung<PERSON> Kami');
  const pageDescription = "Hubungi tim Arrahmah Handling Service untuk konsultasi, pertanyaan, atau membuat janji temu. Kami siap membantu Anda 24/7.";
  const pageKeywords = "kontak arrahmah, janji temu, hubungi kami, layanan umrah, land arrangement, konsultasi, alamat kantor, nomor telepon";

  const contactSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Arrahmah Handling Service",
    "url": "https://umrahservice.co/contact",
    "logo": "https://www.umrahservice.co/logo-umrahservice.png",
    "image": "https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/0ef586d5607ce8348b025632bfe2a445.jpg", // Default image for contact page
    "contactPoint": [
      {
        "@type": "ContactPoint",
        "telephone": "+62-812-8090-8093",
        "contactType": "customer service",
        "areaServed": "ID",
        "availableLanguage": ["Indonesian", "English", "Arabic"]
      },
      {
        "@type": "ContactPoint",
        "telephone": "+966-54-070-5271",
        "contactType": "customer service",
        "areaServed": "SA",
        "availableLanguage": ["Arabic", "Indonesian", "English"]
      }
    ],
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "Grand Galaxy Park, Ruko No. 81 Blok RGA, Jaka Setia",
      "addressLocality": "Bekasi Selatan",
      "addressRegion": "Jawa Barat",
      "postalCode": "17147",
      "addressCountry": "ID"
    }
  };

  return (
    <>
      <SEO 
        title={pageTitle}
        description={pageDescription}
        keywords={pageKeywords}
        schema={contactSchema}
      />
      <div className="min-h-screen flex flex-col bg-background text-white">
        <Navbar />
        <main className="flex-grow">
          <section className="pt-32 pb-20 bg-gradient-to-b from-background to-secondary relative overflow-hidden">
            <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
            <div className="absolute -top-24 -right-24 w-72 h-72 bg-amber-500/10 rounded-full blur-3xl -z-10"></div>
            <div className="absolute -bottom-24 -left-24 w-72 h-72 bg-amber-500/10 rounded-full blur-3xl -z-10"></div>
            
            <div className="container mx-auto px-4 md:px-6 text-center relative">
              <motion.div
                initial={{ opacity: 0, y: -30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.7, type: "spring", stiffness: 100 }}
              >
                <div className="inline-block p-4 bg-gray-800/60 rounded-2xl mb-6 border border-gray-700">
                  <Mail className="w-10 h-10 text-[#FFD700]" />
                </div>
                <h1 className="text-4xl md:text-6xl font-extrabold mb-6 tracking-tight gradient-text">
                  {pageTitle}
                </h1>
                <p className="text-lg md:text-xl text-gray-400 max-w-3xl mx-auto">
                  Kami siap menjadi mitra terbaik Anda. Sampaikan kebutuhan Anda, dan biarkan tim profesional kami memberikan solusi terbaik.
                </p>
              </motion.div>
            </div>
          </section>

          <section className="py-20 bg-secondary">
            <div className="container mx-auto px-4 md:px-6">
              <div className="grid grid-cols-1 lg:grid-cols-5 gap-12">
                <motion.div
                  initial={{ opacity: 0, x: -30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.8, ease: "easeOut" }}
                  className="lg:col-span-3"
                >
                  <Tabs defaultValue="message" className="w-full">
                    <TabsList className="grid w-full grid-cols-2 bg-gray-900/70 p-1.5 h-auto rounded-lg border border-gray-700">
                      <TabsTrigger value="message" className="data-[state=active]:bg-gray-800 data-[state=active]:text-amber-400 data-[state=active]:shadow-md rounded-md py-2.5 transition-all duration-300 flex items-center justify-center gap-2">
                        <Mail size={18} /> {getTranslation('contactFormTitle', 'Kirim Pesan')}
                      </TabsTrigger>
                      <TabsTrigger value="appointment" className="data-[state=active]:bg-gray-800 data-[state=active]:text-amber-400 data-[state=active]:shadow-md rounded-md py-2.5 transition-all duration-300 flex items-center justify-center gap-2">
                        <CalendarPlus size={18} /> {getTranslation('appointmentFormTitle', 'Buat Janji Temu')}
                      </TabsTrigger>
                    </TabsList>
                    <TabsContent value="message" className="mt-6">
                      <ContactForm />
                    </TabsContent>
                    <TabsContent value="appointment" className="mt-6">
                      <AppointmentForm />
                    </TabsContent>
                  </Tabs>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, x: 30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
                  className="lg:col-span-2 space-y-8"
                >
                  <ContactInfo />
                  <ContactMap />
                </motion.div>
              </div>
            </div>
          </section>
        </main>
        <Footer />
      </div>
    </>
  );
};

export default ContactPage;
import React, { useContext } from 'react';
import { Link } from 'react-router-dom';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import { Button } from '@/components/ui/button';
import { Home, Search, ArrowLeft } from 'lucide-react';
import { LanguageContext } from '@/contexts/LanguageContext';
import SEO from '@/components/shared/SEO';

const NotFoundPage = () => {
  const { translations } = useContext(LanguageContext);

  const getTranslation = (key, fallback) => translations[key] || fallback;

  return (
    <>
      <SEO
        title={getTranslation('postNotFoundTitle', 'Halaman Tidak Ditemukan')}
        description={getTranslation('postNotFoundMessage', 'Ma<PERSON>, halaman yang Anda cari tidak ada atau telah dipindahkan.')}
        noIndex={true}
      />
      <div className="min-h-screen flex flex-col bg-background text-white">
        <Navbar />
        <main className="flex-grow flex items-center justify-center py-20 bg-grid-pattern">
          <div className="container mx-auto px-4 md:px-6 text-center">
            <div className="max-w-2xl mx-auto bg-secondary/50 backdrop-blur-sm p-8 md:p-12 rounded-2xl shadow-2xl border border-gray-700/50">
              <div className="mb-6">
                <img src="https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/a3809670215c8e8e446c4a86889e8302.jpg" alt="Pemandangan Islami yang menenangkan" className="w-32 h-32 mx-auto rounded-full object-cover border-4 border-amber-400 shadow-lg" />
              </div>
              <h1 className="text-6xl md:text-8xl font-extrabold gradient-text mb-4">404</h1>
              <h2 className="text-2xl md:text-3xl font-bold text-white mb-4">
                {getTranslation('postNotFoundTitle', 'Halaman Tidak Ditemukan')}
              </h2>
              <p className="text-gray-300 max-w-md mx-auto mb-10">
                {getTranslation('postNotFoundMessage', 'Maaf, halaman yang Anda cari tidak ada atau telah dipindahkan. Mungkin Anda bisa menemukan apa yang Anda cari di bawah ini.')}
              </p>
              <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
                <Button asChild className="gold-gradient text-black font-semibold w-full sm:w-auto">
                  <Link to="/">
                    <Home className="mr-2 h-4 w-4" />
                    {getTranslation('home', 'Beranda')}
                  </Link>
                </Button>
                <Button asChild variant="outline" className="border-amber-400 text-amber-400 hover:bg-amber-400 hover:text-black w-full sm:w-auto">
                  <Link to="/blog">
                    <Search className="mr-2 h-4 w-4" />
                    {getTranslation('Blog', 'Blog')}
                  </Link>
                </Button>
                <Button asChild variant="outline" className="border-amber-400 text-amber-400 hover:bg-amber-400 hover:text-black w-full sm:w-auto">
                  <Link to="/contact">
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    {getTranslation('contact', 'Hubungi Kami')}
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    </>
  );
};

export default NotFoundPage;
import React, { useState, useEffect, useCallback, useContext } from 'react';
import SEO from '@/components/shared/SEO';
import { motion } from 'framer-motion';
import { useToast } from "@/components/ui/use-toast";
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import OrderForm from '@/components/order/OrderForm';
import OrderSummary from '@/components/order/OrderSummary';
import PackageDetailSidebar from '@/components/order/PackageDetailSidebar';
import { Button } from "@/components/ui/button";
import { Send, Layers } from 'lucide-react';
import { pricingStructure, getPrice } from '@/data/pricingStructure';
import { v4 as uuidv4 } from 'uuid';
import { LanguageContext } from '@/contexts/LanguageContext';
import { Card, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { useLocation, useNavigate } from 'react-router-dom';
import { packagesData } from '@/components/pricing/la_b2b/packagesData';
import { orderStorageService } from '@/services/orderStorageService';

const initialFormData = {
    bookerInfo: { travelName: '', picName: '', whatsapp: '', email: '', jamaahOrigin: '' },
    packageType: 'la_b2b',
    selectedLaB2bPackage: null,
    roomComposition: { quad: '', triple: '', double: '', single: '' },
    totalPax: 0,
    packagePricing: {
        category: 'basic',
        airportService: 'JED/MED – Kedatangan Saja',
        alBaikArrival: false,
        alBaikDeparture: false,
    },
    optionalAddons: {
        culinary: { al_rumansiah: false },
        thaif_tour: {
            bus_charter: false,
            cable_car: false,
            tobogan: false,
            lunch: false,
        },
        haramain_train: {
            makkah_madinah: false,
            madinah_makkah: false,
            madinah_jed: false,
            jed_madinah: false,
        },
    },
    mutawwifRequest: {
        needed: false,
    },
    flightDetails: {
        departureDate: null,
        departureAirline: '',
        departureFlightNumber: '',
        returnDate: null,
        returnAirline: '',
        returnFlightNumber: '',
        ticketStatus: 'Belum Ada',
    },
    hotelDetails: {
        hotels: [{ id: uuidv4(), hotelName: '', city: 'Makkah', checkIn: null, nights: '', checkOut: null }]
    },
    visaStatus: 'Belum punya visa',
    documents: {
        roomlist: null,
        ticket: null,
        visa: null,
        manifest: null,
    },
    notes: '',
};

const packageTypeLabels = {
    handling_service: 'Paket Handling Service',
    bundling_visa_handling: 'Paket Bundling Visa + Handling',
    la_b2b: 'Paket LA B2B',
    handling_airport_only: 'Paket Handling Airport Only',
    custom_request: 'Permintaan Custom',
};

const MAX_PAX_PER_BUS = 47;

const OrderPage = () => {
    const { toast } = useToast();
    const navigate = useNavigate();
    const [formData, setFormData] = useState(initialFormData);
    const [summary, setSummary] = useState({
        packageName: '',
        totalPax: 0,
        pricePerPax: 0,
        subtotal: 0,
        addons: [],
        total: 0,
        groups: 0,
        groupDetails: [],
    });
    const [hoveredPackage, setHoveredPackage] = useState(null);
    const { translations } = useContext(LanguageContext);
    const getTranslation = (key, fallback) => translations[key] || fallback;
    const location = useLocation();

    useEffect(() => {
        const { state } = location;
        if (state) {
            if (state.selectedPackageId) {
                const allPackages = [...packagesData.program10Nights, ...packagesData.program7Nights];
                const pkg = allPackages.find(p => p.id === state.selectedPackageId);
                if (pkg) {
                    setFormData(prev => ({
                        ...prev,
                        packageType: 'la_b2b',
                        selectedLaB2bPackage: pkg,
                    }));
                }
            } else if (state.orderDetails) {
                const { packageType, category, airportService } = state.orderDetails;
                setFormData(prev => {
                    const newPackagePricing = { ...prev.packagePricing };
                    if (category) {
                        newPackagePricing.category = category.toLowerCase();
                    }
                    if (airportService) {
                        newPackagePricing.airportService = airportService;
                    }

                    return {
                        ...prev,
                        packageType: packageType,
                        packagePricing: newPackagePricing,
                    };
                });
            }
        }
        window.history.replaceState({}, document.title);
    }, [location]);

    const handleInputChange = useCallback((section, field, value) => {
        setFormData(prev => {
            if (field) {
                return { ...prev, [section]: { ...prev[section], [field]: value } };
            }
            return { ...prev, [section]: value };
        });
    }, []);
    
    const onPackageTypeChange = (value) => {
        if (value === 'custom_request') {
             navigate('/order/custom-request', { state: { formData: formData }});
        } else {
            handleInputChange('packageType', null, value);
            if (value !== 'la_b2b') {
                handleInputChange('selectedLaB2bPackage', null, null);
            }
        }
    };

    const onSelectLaB2bPackage = (pkg) => {
        handleInputChange('selectedLaB2bPackage', null, pkg);
    };

    const distributePaxEvenly = (totalPax, numBuses) => {
        if (numBuses === 0) return [];
        const basePax = Math.floor(totalPax / numBuses);
        const remainder = totalPax % numBuses;
        const distribution = [];
        for (let i = 0; i < numBuses; i++) {
            distribution.push(basePax + (i < remainder ? 1 : 0));
        }
        return distribution;
    };
    
    useEffect(() => {
        const { quad, triple, double, single } = formData.roomComposition;
        const total = (parseInt(quad, 10) || 0) * 4 +
                      (parseInt(triple, 10) || 0) * 3 +
                      (parseInt(double, 10) || 0) * 2 +
                      (parseInt(single, 10) || 0) * 1;
        setFormData(prev => ({ ...prev, totalPax: total }));
    }, [formData.roomComposition]);


    useEffect(() => {
        const { packageType, totalPax, packagePricing, optionalAddons, selectedLaB2bPackage, roomComposition, mutawwifRequest, hotelDetails } = formData;
        
        if (totalPax === 0) {
            setSummary({ packageName: '', totalPax: 0, pricePerPax: 0, subtotal: 0, addons: [], total: 0, groups: 0, groupDetails: [] });
            return;
        }

        const numBuses = Math.ceil(totalPax / MAX_PAX_PER_BUS);
        const paxDistribution = distributePaxEvenly(totalPax, numBuses);

        let subtotal = 0;
        let groupDetails = [];
        let packageName = packageTypeLabels[packageType] || '';

        if (packageType === 'handling_service' || packageType === 'bundling_visa_handling') {
            packageName = `${packageTypeLabels[packageType]} - ${packagePricing.category.charAt(0).toUpperCase() + packagePricing.category.slice(1)}`;
            groupDetails = paxDistribution.map((pax, index) => {
                const pricePerPax = getPrice(packageType, packagePricing.category, pax);
                const totalGroupPrice = pricePerPax * pax;
                subtotal += totalGroupPrice;
                return { 
                    bus: index + 1, 
                    pax: pax, 
                    package: packagePricing.category, 
                    price_per_pax: pricePerPax,
                    total_group_price: totalGroupPrice
                };
            });
        } else if (packageType === 'handling_airport_only') {
            packageName = `${packageTypeLabels[packageType]}`;
            const serviceKey = packagePricing.airportService;
            const basePrice = pricingStructure.handling_airport_only.prices[serviceKey] || 0;
            
            let alBaikCost = 0;
            if (packagePricing.alBaikArrival && !serviceKey.includes('Kepulangan Saja')) {
                alBaikCost += pricingStructure.handling_airport_only.alBaik;
            }
            if (packagePricing.alBaikDeparture && !serviceKey.includes('Kedatangan Saja')) {
                alBaikCost += pricingStructure.handling_airport_only.alBaik;
            }

            const pricePerPaxWithAlBaik = basePrice + alBaikCost;
            subtotal = pricePerPaxWithAlBaik * totalPax;
            
            groupDetails = paxDistribution.map((pax, index) => {
                 const totalGroupPrice = pricePerPaxWithAlBaik * pax;
                 return { 
                    bus: index + 1, 
                    pax: pax, 
                    price_per_pax: pricePerPaxWithAlBaik,
                    total_group_price: totalGroupPrice
                 }
            });

        } else if (packageType === 'la_b2b' && selectedLaB2bPackage) {
            packageName = getTranslation(selectedLaB2bPackage.nameKey, selectedLaB2bPackage.name);
            const { quad, triple, double, single } = roomComposition;
            const numQuad = parseInt(quad, 10) || 0;
            const numTriple = parseInt(triple, 10) || 0;
            const numDouble = parseInt(double, 10) || 0;
            const numSingle = parseInt(single, 10) || 0;
            
            groupDetails = paxDistribution.map((pax, index) => {
                 const pricingTier = selectedLaB2bPackage.pricing.find(p => {
                    const paxRange = p.pax.split(' - ').map(Number);
                    return pax >= paxRange[0] && pax <= (paxRange[1] || Infinity);
                });
                
                let pricePerPax = 0;
                let totalGroupPrice = 0;

                if (pricingTier) {
                    const mainPricingTier = selectedLaB2bPackage.pricing.find(p => {
                        const paxRange = p.pax.split(' - ').map(Number);
                        return totalPax >= paxRange[0] && totalPax <= (paxRange[1] || Infinity);
                    });

                    let totalPackageCost = 0;
                    if (mainPricingTier) {
                        totalPackageCost = (numQuad * mainPricingTier.quad * 4) + 
                                           (numTriple * mainPricingTier.triple * 3) + 
                                           (numDouble * mainPricingTier.double * 2) +
                                           (numSingle * (mainPricingTier.single || mainPricingTier.double) * 1);
                    }
                    
                    const avgPricePerPax = totalPax > 0 ? totalPackageCost / totalPax : 0;
                    pricePerPax = avgPricePerPax;
                    totalGroupPrice = pricePerPax * pax;
                    subtotal += totalGroupPrice;
                }

                return {
                    bus: index + 1,
                    pax: pax,
                    price_per_pax: pricePerPax,
                    total_group_price: totalGroupPrice
                };
            });
        }

        const addonsCost = [];
        const addonsPrices = pricingStructure.optional_addons;
        const { thaif_tour, haramain_train, culinary } = optionalAddons;

        if (mutawwifRequest.needed && (packageType === 'handling_service' || packageType === 'bundling_visa_handling')) {
            const totalNights = hotelDetails.hotels.reduce((acc, hotel) => acc + (parseInt(hotel.nights, 10) || 0), 0);
            const mutawwifDuration = totalNights > 0 ? totalNights + 1 : 0;

            if (mutawwifDuration > 0 && totalPax > 0) {
                const { cost_per_day_sr, sr_to_idr_rate, sr_to_usd_rate } = addonsPrices.mutawwif;
                const costPerPax = (cost_per_day_sr * sr_to_idr_rate * mutawwifDuration) / totalPax * sr_to_usd_rate;
                const totalMutawwifCost = costPerPax * totalPax;
                addonsCost.push({ name: `Mutawwif (${mutawwifDuration} hari)`, cost: totalMutawwifCost, perPax: costPerPax });
            }
        }

        if (culinary.al_rumansiah) addonsCost.push({ name: 'Makan di Al Rumansiah', cost: addonsPrices.culinary.al_rumansiah * totalPax });

        const thaifBuses = thaif_tour.bus_charter ? numBuses : 0;
        if (thaif_tour.bus_charter) addonsCost.push({ name: `Tour Thaif: Bus Charter (${thaifBuses} unit)`, cost: addonsPrices.thaif_tour.bus_charter * thaifBuses });
        if (thaif_tour.cable_car) addonsCost.push({ name: 'Tour Thaif: Cable Car', cost: addonsPrices.thaif_tour.cable_car * totalPax });
        if (thaif_tour.tobogan) addonsCost.push({ name: 'Tour Thaif: Tobogan', cost: addonsPrices.thaif_tour.tobogan * totalPax });
        if (thaif_tour.lunch) addonsCost.push({ name: 'Tour Thaif: Makan Siang', cost: addonsPrices.thaif_tour.lunch * totalPax });

        if (haramain_train.makkah_madinah) addonsCost.push({ name: 'Kereta: Makkah ➝ Madinah', cost: addonsPrices.haramain_train.makkah_madinah * totalPax });
        if (haramain_train.madinah_makkah) addonsCost.push({ name: 'Kereta: Madinah ➝ Makkah', cost: addonsPrices.haramain_train.madinah_makkah * totalPax });
        if (haramain_train.madinah_jed) addonsCost.push({ name: 'Kereta: Madinah ➝ JED', cost: addonsPrices.haramain_train.madinah_jed * totalPax });
        if (haramain_train.jed_madinah) addonsCost.push({ name: 'Kereta: JED ➝ Madinah', cost: addonsPrices.haramain_train.jed_madinah * totalPax });
        
        const totalAddonsCost = addonsCost.reduce((acc, addon) => acc + addon.cost, 0);
        const total = subtotal + totalAddonsCost;
        const pricePerPax = totalPax > 0 ? total / totalPax : 0;

        setSummary({ packageName, totalPax, pricePerPax, subtotal, addons: addonsCost, total, groups: numBuses, groupDetails });

    }, [formData, getTranslation, toast]);

    const handleSubmit = (e) => {
        e.preventDefault();
        
        try {
            const newOrder = orderStorageService.addOrder(formData, summary);
            toast({
                title: "Order Berhasil Diterima!",
                description: "Anda akan diarahkan ke halaman detail pesanan.",
            });
            navigate(`/order/success/${newOrder.id}`);
        } catch (error) {
            toast({
                title: "Gagal Mencatat Order",
                description: "Terjadi kesalahan saat menyimpan pesanan.",
                variant: "destructive",
            });
        }
    };
    
    const sidebarPackage = hoveredPackage || formData.selectedLaB2bPackage;

    return (
        <>
            <SEO
                title="Formulir Pemesanan Layanan"
                description="Pesan layanan handling umrah, visa, hotel, dan lainnya melalui formulir pemesanan resmi Arrahmah Handling Service."
            />
            <Navbar />
            <motion.main
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5 }}
                className="bg-background pt-32 pb-20"
            >
                <div className="container mx-auto px-4 md:px-6">
                     <Card className="bg-gradient-to-br from-gray-800/60 to-gray-900/80 border-gray-700/60 rounded-3xl shadow-2xl overflow-hidden backdrop-blur-sm mb-12">
                        <CardHeader className="text-center pb-4">
                            <Layers className="mx-auto h-12 w-12 text-amber-400" />
                            <CardTitle className="text-4xl font-bold text-white tracking-tight mt-4">Formulir Pemesanan Layanan</CardTitle>
                            <CardDescription className="text-lg text-gray-400 max-w-3xl mx-auto pt-2">
                               Pilih jenis layanan yang Anda butuhkan. Formulir akan menyesuaikan secara dinamis untuk mempermudah pemesanan Anda.
                            </CardDescription>
                        </CardHeader>
                    </Card>

                    <form onSubmit={handleSubmit} className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-start">
                        <div className="lg:col-span-2 space-y-8">
                           <OrderForm 
                             formData={formData} 
                             handleInputChange={handleInputChange}
                             setFormData={setFormData}
                             onPackageTypeChange={onPackageTypeChange}
                             onSelectLaB2bPackage={onSelectLaB2bPackage}
                             setHoveredPackage={setHoveredPackage}
                           />
                        </div>

                        <div className="lg:col-span-1 lg:sticky lg:top-28 flex flex-col" style={{ height: 'calc(100vh - 7rem)' }}>
                             <div className="flex-grow overflow-y-auto pr-2">
                                <div className="space-y-6">
                                    <OrderSummary summary={summary} formData={formData} />
                                    {formData.packageType === 'la_b2b' && sidebarPackage && (
                                        <PackageDetailSidebar pkg={sidebarPackage} translations={translations} />
                                    )}
                                </div>
                            </div>
                            <div className="flex-shrink-0 pt-4 bg-background">
                                <Button type="submit" size="lg" className="w-full gold-gradient text-black font-bold text-lg py-6" disabled={formData.totalPax === 0}>
                                    <Send className="mr-2 h-5 w-5"/>
                                    Kirim & Lihat Detail Order
                                </Button>
                            </div>
                        </div>
                    </form>
                </div>
            </motion.main>
            <Footer />
        </>
    );
};

export default OrderPage;
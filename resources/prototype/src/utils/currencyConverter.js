export const convertCurrency = (amount, fromCurrency, toCurrency, rates) => {
  if (!rates || !rates[fromCurrency] || !rates[toCurrency]) {
    console.error("Rates object or currency codes are invalid for conversion.", { fromCurrency, toCurrency, rates });
    return amount;
  }
  const amountInUSD = amount / rates[fromCurrency];
  return amountInUSD * rates[toCurrency];
};

const formatToShort = (num, lang) => {
  const locale = lang === 'id' ? 'id-ID' : 'en-US';
  const numberFormatter = new Intl.NumberFormat(locale, {
    minimumFractionDigits: 1,
    maximumFractionDigits: 1,
  });

  if (num >= 1e9) {
    const val = num / 1e9;
    const unit = lang === 'id' ? ' M' : ' B';
    return numberFormatter.format(val) + unit;
  }
  if (num >= 1e6) {
    const val = num / 1e6;
    const unit = lang === 'id' ? ' Jt' : ' M';
    return numberFormatter.format(val) + unit;
  }
  return new Intl.NumberFormat(locale).format(num);
};

export const formatCurrency = (amount, currency, lang = 'id') => {
  let locale = 'en-US';
  if (lang === 'id') locale = 'id-ID';

  const numberFormatter = new Intl.NumberFormat(locale, {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  });
  
  const roundedAmount = Math.round(amount);

  switch (currency) {
    case 'SAR':
      return { value: numberFormatter.format(roundedAmount), unit: '' };
    case 'IDR':
      if (roundedAmount >= 1000000) {
        return { value: 'Rp ', unit: formatToShort(roundedAmount, lang) };
      }
      return { value: 'Rp', unit: numberFormatter.format(roundedAmount) };
    case 'USD':
      return { value: '$', unit: numberFormatter.format(roundedAmount) };
    default:
      const options = { style: 'currency', currency, minimumFractionDigits: 0, maximumFractionDigits: 0 };
      if (lang === 'ar') {
        options.currencyDisplay = 'code';
        const formatted = new Intl.NumberFormat('ar-SA-u-nu-latn', options).format(roundedAmount);
        return { value: formatted.replace(currency, ''), unit: ' SR' };
      }
      const formatted = new Intl.NumberFormat(locale, options).format(roundedAmount);
      return { value: formatted, unit: '' };
  }
};
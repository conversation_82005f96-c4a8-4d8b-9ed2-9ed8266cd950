import { differenceInDays, isValid, addDays } from 'date-fns';

export function calculateNights(checkIn, checkOut) {
    if (checkIn && checkOut && isValid(new Date(checkIn)) && isValid(new Date(checkOut))) {
        const diff = differenceInDays(new Date(checkOut), new Date(checkIn));
        return diff > 0 ? diff : 0;
    }
    return 0;
}

export function calculateCheckoutDate(checkIn, nights) {
  if (checkIn && nights && isValid(new Date(checkIn)) && !isNaN(parseInt(nights, 10))) {
    return addDays(new Date(checkIn), parseInt(nights, 10));
  }
  return null;
}
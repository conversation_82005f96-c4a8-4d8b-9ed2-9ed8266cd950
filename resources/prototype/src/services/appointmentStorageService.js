import { v4 as uuidv4 } from 'uuid';

const APPOINTMENTS_KEY = 'appointments';

const getAppointments = () => {
  try {
    const appointmentsJson = localStorage.getItem(APPOINTMENTS_KEY);
    return appointmentsJson ? JSON.parse(appointmentsJson) : [];
  } catch (error) {
    console.error("Could not get appointments from localStorage", error);
    return [];
  }
};

const addAppointment = (appointmentData) => {
  const appointments = getAppointments();
  const newAppointment = {
    id: uuidv4(),
    ...appointmentData,
    createdAt: new Date().toISOString(),
    status: 'pending',
  };
  try {
    appointments.push(newAppointment);
    localStorage.setItem(APPOINTMENTS_KEY, JSON.stringify(appointments));
  } catch (error) {
    console.error("Could not save appointment to localStorage", error);
  }
  return newAppointment;
};

export const appointmentStorageService = {
  getAppointments,
  addAppointment,
};
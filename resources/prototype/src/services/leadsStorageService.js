import { v4 as uuidv4 } from 'uuid';
import { appointmentStorageService } from './appointmentStorageService';
import { orderStorageService } from './orderStorageService';

const LEADS_KEY = 'contactSubmissions';

export const leadsStorageService = {
  loadLeads: () => {
    try {
      const storedLeads = localStorage.getItem(LEADS_KEY);
      return storedLeads ? JSON.parse(storedLeads) : [];
    } catch (error) {
      console.error('Error loading leads from localStorage:', error);
      return [];
    }
  },

  saveLeads: (leads) => {
    try {
      localStorage.setItem(LEADS_KEY, JSON.stringify(leads));
    } catch (error)
    {
      console.error('Error saving leads to localStorage:', error);
      throw error;
    }
  },

  addLead: (leadData, source = 'Formulir Kontak') => {
    try {
      const existingLeads = leadsStorageService.loadLeads();
      const newLead = {
        id: `lead-${Date.now()}-${uuidv4().slice(0, 8)}`,
        ...leadData,
        date: new Date().toISOString(),
        status: 'Baru',
        source: source,
        type: 'Kontak',
      };
      const updatedLeads = [newLead, ...existingLeads];
      leadsStorageService.saveLeads(updatedLeads);
      return newLead;
    } catch (error) {
      console.error('Error adding lead:', error);
      throw error;
    }
  },

  loadAllLeads: () => {
    const contactLeads = leadsStorageService.loadLeads();
    const appointmentLeads = appointmentStorageService.getAppointments().map(app => ({
      id: app.id,
      name: app.name,
      email: app.email,
      phone: app.phone,
      subject: `Janji Temu: ${new Date(app.date).toLocaleDateString('id-ID')} jam ${app.time}`,
      message: app.notes || 'Tidak ada catatan.',
      source: 'Formulir Janji Temu',
      date: app.createdAt || new Date().toISOString(),
      status: app.status,
      type: 'Janji Temu',
    }));
    return [...contactLeads, ...appointmentLeads];
  },

  loadAllLeadsAndOrders: () => {
    const allLeads = leadsStorageService.loadAllLeads();
    const allOrders = orderStorageService.getOrders().map(order => ({
        id: order.id,
        name: order.formData.bookerInfo.travelName,
        email: order.formData.bookerInfo.email,
        phone: order.formData.bookerInfo.whatsapp,
        subject: `Order: ${order.summary.packageName}`,
        message: `Total ${order.summary.totalPax} pax, senilai $${order.summary.total.toFixed(2)}`,
        source: 'Formulir Order',
        date: order.createdAt,
        status: order.status,
        type: 'Order',
    }));
    return [...allLeads, ...allOrders];
  }
};
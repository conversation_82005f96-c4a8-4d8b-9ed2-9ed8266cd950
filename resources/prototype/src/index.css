@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --radius: 0.5rem;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 50%;
  }
}

@layer base {
  html {
    scroll-behavior: smooth;
  }
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', sans-serif;
  }
  h1, h2, h3, h4, h5, h6 {
    @apply font-bold;
  }
  h1 { @apply text-4xl sm:text-5xl md:text-6xl; }
  h2 { @apply text-3xl sm:text-4xl; }
  h3 { @apply text-2xl sm:text-3xl; }

  .gradient-text {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-amber-400 via-amber-500 to-yellow-600;
  }
  .gold-gradient {
    @apply bg-gradient-to-r from-amber-400 via-amber-500 to-yellow-600 hover:from-amber-500 hover:to-yellow-700;
  }
  .shadow-glow {
    box-shadow: 0 0 15px 5px rgba(255, 215, 0, 0.2), 0 0 30px 10px rgba(255, 165, 0, 0.1);
  }
}

@layer components {
  .sidebar-tab-trigger {
    @apply flex items-center w-full p-4 text-left rounded-lg transition-all duration-200 ease-in-out;
    @apply text-gray-300 hover:bg-gray-700/60 hover:text-white;
    @apply data-[state=active]:bg-amber-500 data-[state=active]:text-black data-[state=active]:shadow-lg data-[state=active]:scale-105;
  }
}

@layer utilities {
  .bg-grid-pattern {
    background-image: linear-gradient(rgba(255, 255, 255, 0.07) 1px, transparent 1px), linear-gradient(90deg, rgba(255, 255, 255, 0.07) 1px, transparent 1px);
    background-size: 2rem 2rem;
  }
}

.view-toggle-btn[data-active='true'] {
  @apply bg-primary text-black;
}

.view-toggle-btn[data-active='false'] {
  @apply text-primary border-primary hover:bg-primary/10;
}

.contact-form-input {
  @apply w-full px-4 py-3 rounded-md bg-gray-700 border border-gray-600 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#FFD700] focus:border-transparent transition-all duration-200;
}

.faq-sidebar-link {
  @apply flex items-center w-full px-4 py-3 text-left text-gray-300 rounded-lg transition-colors duration-200 ease-in-out;
}
.faq-sidebar-link:hover {
  @apply bg-gray-700/60 text-white;
}
.faq-sidebar-link[data-active='true'] {
  @apply bg-gradient-to-r from-amber-500/20 to-amber-500/10 text-amber-300 font-semibold border-l-2 border-amber-400;
}
.faq-sidebar-link[data-active='true'] .faq-sidebar-icon {
  @apply text-amber-400;
}


/* Blog Post Content Styling */
.prose {
  color: #E5E7EB;
  max-width: none;
}

.prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
  color: #FFD700;
  font-weight: bold;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.prose h1 {
  font-size: 2rem;
  line-height: 1.2;
}

.prose h2 {
  font-size: 1.5rem;
  line-height: 1.3;
}

.prose h3 {
  font-size: 1.25rem;
  line-height: 1.4;
}

.prose p {
  margin-bottom: 1.5rem;
  line-height: 1.8;
  color: #D1D5DB;
}

.prose ul, .prose ol {
  margin: 1.5rem 0;
  padding-left: 2rem;
}

.prose li {
  margin: 0.75rem 0;
  line-height: 1.7;
}

.prose blockquote {
  border-left: 4px solid #FFD700;
  padding: 1rem 1.5rem;
  margin: 2rem 0;
  font-style: italic;
  color: #FFD700;
  background-color: rgba(255, 215, 0, 0.1);
  border-radius: 0.375rem;
}

.prose table {
  width: 100%;
  border-collapse: collapse;
  margin: 2rem 0;
  font-size: 0.9rem;
}

.prose th, .prose td {
  border: 1px solid #6B7280;
  padding: 12px;
  text-align: left;
}

.prose th {
  background-color: #374151;
  color: white;
  font-weight: bold;
}

.prose tr:nth-child(even) {
  background-color: #1F2937;
}

.prose a {
  color: #FFD700;
  text-decoration: underline;
  transition: color 0.2s ease;
}

.prose a:hover {
  color: #FFC107;
}

.prose strong {
  color: #FFD700;
  font-weight: bold;
}

.prose img {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  margin: 1.5rem 0;
}

.prose code {
  background-color: #374151;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  color: #FFD700;
}

.prose pre {
  background-color: #1F2937;
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1.5rem 0;
}

.prose pre code {
  background-color: transparent;
  padding: 0;
  color: #E5E7EB;
}
import React from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { ChevronRight, CheckCircle, ArrowRight } from 'lucide-react';

const SlideComponent = ({ slide, direction }) => {
    const variants = {
        enter: (direction) => ({
            x: direction > 0 ? '100%' : '-100%',
            opacity: 0,
            scale: 0.9
        }),
        center: {
            zIndex: 1,
            x: 0,
            opacity: 1,
            scale: 1
        },
        exit: (direction) => ({
            zIndex: 0,
            x: direction < 0 ? '100%' : '-100%',
            opacity: 0,
            scale: 0.9
        })
    };

    const renderSlideContent = () => {
        const Icon = slide.icon;
        switch (slide.type) {
            case 'cover':
                return (
                    <div className="text-center flex flex-col items-center justify-center h-full p-4">
                        <motion.div initial={{ scale: 0.5, opacity: 0 }} animate={{ scale: 1, opacity: 1 }} transition={{ delay: 0.2, duration: 0.5 }}>
                            <img alt="Arrahmah Handling Service Logo" className="h-24 w-auto mb-8 mx-auto" src="/favicon.svg" />
                        </motion.div>
                        <h1 className="text-4xl md:text-5xl font-bold gold-gradient-text mb-4 max-w-4xl">{slide.title}</h1>
                        <p className="text-xl md:text-2xl text-gray-200 max-w-3xl">{slide.subtitle}</p>
                    </div>
                );
            case 'welcome':
                return (
                    <div className="text-center flex flex-col items-center justify-center h-full p-4">
                        <h2 className="text-3xl md:text-4xl font-semibold text-white mb-4">{slide.title}</h2>
                        <p className="text-xl md:text-2xl text-gray-300 max-w-3xl">{slide.subtitle}</p>
                    </div>
                );
            case 'bullet_visual':
                return (
                    <div className="w-full max-w-4xl p-4">
                        <h2 className="text-3xl md:text-4xl font-bold text-white text-center mb-12">{slide.title}</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                            {slide.items.map((item, index) => {
                                const ItemIcon = item.icon;
                                return (
                                    <motion.div key={index} className="flex items-start space-x-4" initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.2 * index }}>
                                        <div className="flex-shrink-0 bg-amber-400/10 p-3 rounded-full">
                                            <ItemIcon className="w-8 h-8 text-amber-400" />
                                        </div>
                                        <p className="text-lg text-gray-200 mt-2">{item.text}</p>
                                    </motion.div>
                                );
                            })}
                        </div>
                    </div>
                );
            case 'solution':
                return (
                    <div className="w-full max-w-4xl p-4">
                        <h2 className="text-3xl md:text-4xl font-bold text-white text-center mb-12">{slide.title}</h2>
                        <div className="space-y-6">
                            {slide.points.map((item, index) => {
                                const ItemIcon = item.icon;
                                return (
                                    <motion.div key={index} className="flex items-center bg-white/5 p-4 rounded-lg" initial={{ opacity: 0, x: -20 }} animate={{ opacity: 1, x: 0 }} transition={{ delay: 0.2 * index }}>
                                        <ItemIcon className="w-10 h-10 text-green-400 mr-6 flex-shrink-0" />
                                        <p className="text-lg text-gray-200">{item.text}</p>
                                    </motion.div>
                                );
                            })}
                        </div>
                    </div>
                );
            case 'simple_list':
                return (
                    <div className="w-full max-w-4xl text-center">
                        <div className="flex items-center justify-center mb-8">
                            {Icon && <Icon className="w-12 h-12 text-amber-400 mr-6" />}
                            <h2 className="text-4xl md:text-5xl font-bold text-white">{slide.title}</h2>
                        </div>
                        <div className="grid grid-cols-1 text-left mx-auto max-w-md gap-y-3">
                            {slide.items.map((item, index) => (
                                <div key={index} className="text-xl">
                                    <span className="font-semibold text-amber-300">{item.label}: </span>
                                    <span className="text-gray-200">{item.value}</span>
                                </div>
                            ))}
                        </div>
                    </div>
                );
            case 'philosophy_full':
                return (
                    <div className="w-full max-w-4xl text-center p-4">
                        <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">{slide.title}</h2>
                        <p className="text-6xl text-amber-400 font-serif my-4">{slide.arabicText}</p>
                        <p className="text-2xl text-gray-300 mb-8">( {slide.translation} )</p>
                        <blockquote className="text-xl text-gray-200 italic border-l-4 border-amber-400 pl-6">
                            {slide.text}
                        </blockquote>
                    </div>
                );
            case 'vision_mission':
                return (
                    <div className="w-full max-w-4xl">
                        <div className="flex items-center justify-center mb-10">
                            {Icon && <Icon className="w-12 h-12 text-amber-400 mr-6" />}
                            <h2 className="text-4xl md:text-5xl font-bold text-white">{slide.title}</h2>
                        </div>
                        <div className="space-y-8">
                            <div className="bg-black/20 p-6 rounded-xl backdrop-blur-sm">
                                <h3 className="text-3xl font-semibold text-amber-400 mb-3">Visi</h3>
                                <p className="text-lg text-gray-200">{slide.vision}</p>
                            </div>
                             <div className="bg-black/20 p-6 rounded-xl backdrop-blur-sm">
                                <h3 className="text-3xl font-semibold text-amber-400 mb-3">Misi</h3>
                                <ul className="space-y-3 text-lg text-gray-200">
                                    {slide.mission.map((item, index) => (
                                         <li key={index} className="flex items-start">
                                            <ChevronRight className="w-6 h-6 text-amber-400 mr-2 mt-1 flex-shrink-0" />
                                            <span>{item}</span>
                                        </li>
                                    ))}
                                </ul>
                            </div>
                        </div>
                    </div>
                );
            case 'content':
                return (
                    <div className="w-full max-w-4xl">
                         <div className="flex items-center justify-center mb-8">
                            {Icon && <Icon className="w-12 h-12 text-amber-400 mr-6" />}
                            <h2 className="text-4xl md:text-5xl font-bold text-white text-center">{slide.title}</h2>
                        </div>
                        <ul className="space-y-4">
                           {slide.list.map((item, index) => (
                               <motion.li key={index} className="flex items-start text-lg text-gray-200 bg-white/5 p-4 rounded-lg backdrop-blur-sm" initial={{ opacity: 0, x: -20 }} animate={{ opacity: 1, x: 0 }} transition={{ delay: 0.15 * index }}>
                                   <CheckCircle className="w-6 h-6 text-green-400 mr-4 mt-1 flex-shrink-0" />
                                   <span>{item}</span>
                               </motion.li>
                           ))}
                       </ul>
                    </div>
                );
            case 'values':
                return (
                    <div className="w-full max-w-5xl">
                        <h2 className="text-4xl md:text-5xl font-bold text-white text-center mb-12">{slide.title}</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            {slide.items.map((value, index) => {
                                const ValueIcon = value.icon;
                                return (
                                    <motion.div key={index} className="bg-white/5 p-6 rounded-xl text-center backdrop-blur-sm" initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.15 * index }}>
                                        <ValueIcon className="w-12 h-12 text-amber-400 mx-auto mb-4" />
                                        <h3 className="text-2xl font-semibold text-amber-300 mb-2">{value.name}</h3>
                                        <p className="text-gray-300">{value.description}</p>
                                    </motion.div>
                                );
                            })}
                        </div>
                    </div>
                );
            case 'grid':
                return (
                     <div className="w-full max-w-5xl">
                         <div className="flex items-center justify-center mb-8">
                            {Icon && <Icon className="w-12 h-12 text-amber-400 mr-6" />}
                            <h2 className="text-4xl md:text-5xl font-bold text-white">{slide.title}</h2>
                        </div>
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-center">
                            {slide.items.map((item, index) => (
                                <motion.div key={index} className="bg-white/5 p-4 rounded-lg text-lg text-gray-200 flex items-center justify-center backdrop-blur-sm" initial={{ opacity: 0, scale: 0.8 }} animate={{ opacity: 1, scale: 1 }} transition={{ delay: 0.1 * index }}>
                                    <span>{item}</span>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                );
            case 'team_visual':
                return (
                    <div className="w-full max-w-4xl text-center">
                        <div className="flex items-center justify-center mb-8">
                            {Icon && <Icon className="w-12 h-12 text-amber-400 mr-6" />}
                            <h2 className="text-4xl md:text-5xl font-bold text-white">{slide.title}</h2>
                        </div>
                        <div className="flex flex-wrap justify-center gap-4 mb-8">
                            {slide.teams.map((team, index) => (
                                <motion.div key={index} className="bg-amber-400 text-black font-semibold px-4 py-2 rounded-full" initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.1 * index }}>
                                    {team}
                                </motion.div>
                            ))}
                        </div>
                        <p className="text-lg text-gray-300 italic">{slide.footer}</p>
                    </div>
                );
            case 'why_us':
                return (
                    <div className="w-full max-w-5xl">
                         <div className="flex items-center justify-center mb-8">
                            {Icon && <Icon className="w-12 h-12 text-amber-400 mr-6" />}
                            <h2 className="text-4xl md:text-5xl font-bold text-white">{slide.title}</h2>
                        </div>
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                            {slide.points.map((point, index) => (
                                <motion.div key={index} className="bg-green-500/10 border border-green-500/30 p-4 rounded-lg flex items-center text-lg text-gray-100 backdrop-blur-sm" initial={{ opacity: 0, scale: 0.8 }} animate={{ opacity: 1, scale: 1 }} transition={{ delay: 0.1 * index }}>
                                    <CheckCircle className="w-6 h-6 text-green-400 mr-3 flex-shrink-0" />
                                    <span>{point}</span>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                );
            case 'flow':
                return (
                    <div className="w-full max-w-5xl">
                        <div className="flex items-center justify-center mb-12">
                           {Icon && <Icon className="w-12 h-12 text-amber-400 mr-6" />}
                           <h2 className="text-4xl md:text-5xl font-bold text-white">{slide.title}</h2>
                       </div>
                       <div className="flex flex-col md:flex-row items-center justify-center gap-2">
                           {slide.steps.map((step, index) => (
                               <React.Fragment key={index}>
                                   <motion.div className="flex flex-col items-center text-center" initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.2 * index }}>
                                       <div className="flex-shrink-0 w-16 h-16 rounded-full bg-amber-400 text-black flex items-center justify-center font-bold text-2xl z-10 mb-3">{index + 1}</div>
                                       <p className="text-md text-gray-200 max-w-[150px]">{step}</p>
                                   </motion.div>
                                   {index < slide.steps.length - 1 && (
                                       <motion.div className="hidden md:block" initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ delay: 0.2 * index + 0.1 }}>
                                            <ArrowRight className="w-12 h-12 text-white/50 mx-2" />
                                       </motion.div>
                                   )}
                               </React.Fragment>
                           ))}
                       </div>
                   </div>
                );
            case 'statistics':
                 return (
                    <div className="w-full max-w-5xl">
                        <div className="flex items-center justify-center mb-12">
                           {Icon && <Icon className="w-12 h-12 text-amber-400 mr-6" />}
                           <h2 className="text-4xl md:text-5xl font-bold text-white">{slide.title}</h2>
                       </div>
                       <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
                           {slide.stats.map((stat, index) => (
                               <motion.div key={index} initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.15 * index }}>
                                   <p className="text-5xl font-bold gold-gradient-text">{stat.value}</p>
                                   <p className="text-lg text-gray-300 mt-2">{stat.label}</p>
                               </motion.div>
                           ))}
                       </div>
                   </div>
                );
            case 'collaboration':
                return (
                    <div className="w-full max-w-4xl text-center">
                        <h2 className="text-4xl md:text-5xl font-bold text-white mb-8">{slide.title}</h2>
                        <ul className="space-y-4 text-xl text-gray-200">
                            {slide.points.map((point, index) => (
                                <motion.li key={index} className="flex items-center justify-center" initial={{ opacity: 0, x: -20 }} animate={{ opacity: 1, x: 0 }} transition={{ delay: 0.2 * index }}>
                                    <CheckCircle className="w-6 h-6 text-green-400 mr-4 flex-shrink-0" />
                                    <span>{point}</span>
                                </motion.li>
                            ))}
                        </ul>
                    </div>
                );
            case 'contact_full':
                return (
                    <div className="w-full max-w-4xl text-center">
                        <h2 className="text-4xl md:text-5xl font-bold text-white mb-12">{slide.title}</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {slide.items.map((item, index) => {
                                const ItemIcon = item.icon;
                                return (
                                    <motion.div key={index} className="bg-white/5 p-4 rounded-lg flex items-center space-x-4 backdrop-blur-sm" initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.15 * index }}>
                                        <ItemIcon className="w-8 h-8 text-amber-400 flex-shrink-0" />
                                        <span className="text-lg text-gray-200">{item.text}</span>
                                    </motion.div>
                                );
                            })}
                        </div>
                    </div>
                );
            default:
                return <div className="text-white">Slide type not found: {slide.type}</div>;
        }
    };

    return (
        <motion.div
            className="absolute w-full h-full flex items-center justify-center p-4 md:p-16"
            variants={variants}
            custom={direction}
            initial="enter"
            animate="center"
            exit="exit"
            transition={{
                x: { type: "spring", stiffness: 300, damping: 30 },
                opacity: { duration: 0.3 }
            }}
        >
            <div className="w-full h-full bg-black/20 rounded-2xl shadow-2xl p-4 md:p-8 flex items-center justify-center backdrop-blur-lg border border-white/10">
                 {renderSlideContent()}
            </div>
        </motion.div>
    );
};

export default SlideComponent;
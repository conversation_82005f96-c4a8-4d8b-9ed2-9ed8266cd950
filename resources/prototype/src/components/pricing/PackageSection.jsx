import React from 'react';
import { motion } from 'framer-motion';
import PlanComparisonTable from '@/components/pricing/PlanComparisonTable';
import PlanCardDisplay from '@/components/pricing/PlanCardDisplay';

const PackageSection = ({ title, subtitle, plans, selectedCurrency, allFeatures, type }) => {
  return (
      <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.1 }}
      >
          <div className='text-center md:text-left mb-10'>
            <h2 className='text-3xl md:text-4xl font-bold text-white'>{title}</h2>
            <p className="text-lg text-gray-400 max-w-3xl mt-2">{subtitle}</p>
          </div>
          
          <div className="mb-16">
            <PlanComparisonTable plans={plans} selectedCurrency={selectedCurrency} allFeatures={allFeatures} />
          </div>

          <div className="mt-16">
            <div className="text-center md:text-left mb-10">
                <h3 className="text-2xl md:text-3xl font-bold text-white">Detail Paket</h3>
                <p className="text-md text-gray-400 mt-1">Rincian fasilitas yang termasuk dalam setiap paket.</p>
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8 xl:gap-10">
                {plans.map((plan, index) => (
                    <PlanCardDisplay key={plan.id || plan.name} plan={plan} index={index} type={type} />
                ))}
            </div>
          </div>
      </motion.div>
  );
};

export default PackageSection;
import React, { useState, useContext, useMemo } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button.jsx';
import { MessageSquare, ChevronsRight, ShieldCheck, Layers, Package, Info, Plane, Briefcase } from 'lucide-react';
import CurrencySelector from '@/components/pricing/CurrencySelector';
import PricingHeader from '@/components/pricing/PricingHeader';
import PricingFaq from '@/components/pricing/PricingFaq';
import { LanguageContext } from '@/contexts/LanguageContext';
import { useContent } from '@/contexts/ContentContext.jsx';
import { Link } from 'react-router-dom';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import AirportHandlingOnly from '@/components/pricing/AirportHandlingOnly';
import LaB2bPackages from '@/components/pricing/la_b2b/LaB2bPackages';
import PackageSection from '@/components/pricing/PackageSection';


const WHATSAPP_NUMBER = '6281289552018';

const generateSpecialOfferLink = (contactName = "Customer Service", lang, getTranslation) => {
  let greeting;
  if (lang === 'ar') {
    greeting = `${getTranslation('greeting_ar', 'السلام عليكم')} ${contactName}`;
  } else if (lang === 'en') {
    greeting = `${getTranslation('greeting_en', 'Hello')} ${contactName}`;
  } else {
    greeting = `${getTranslation('greeting_id', 'Assalamu\'alaikum')} ${contactName}`;
  }

  const messageText = 
    lang === 'ar' ? getTranslation('wa_handling_offer_ar', '،\n\nأرغب في طلب عرض خاص لخدمات المناولة.\n\n(تم إرسال الرسالة من الموقع الإلكتروني)') :
    lang === 'en' ? getTranslation('wa_handling_offer_en', ',\n\nI would like to request a special offer for handling services.\n\n(message sent from website)') :
    getTranslation('wa_handling_offer_id', ',\n\nSaya ingin meminta penawaran khusus untuk layanan handling.\n\n(pesan dikirim dari website)');
  
  const message = encodeURIComponent(`${greeting}${messageText}`);
  return `https://wa.me/${WHATSAPP_NUMBER}?text=${message}`;
};


const VisaRequirements = () => {
  const { translations } = useContext(LanguageContext);
  const getTranslation = (key, fallback) => translations[key] || fallback;
  const requirements = [
    { text: getTranslation('visaReq1', 'Paspor aktif minimal 6 bulan dari tanggal pengajuan.') },
    { text: getTranslation('visaReq2', 'Foto latar putih terbaru, tanpa aksesoris kepala, mata terbuka jelas.') },
    { text: getTranslation('visaReq3', 'Scan paspor berwarna (ukuran maksimal 300KB, format JPEG/JPG).') },
    { text: getTranslation('visaReq4', 'Jamaah tidak terdaftar dalam daftar blacklist / pelanggaran imigrasi sebelumnya.') },
    { text: getTranslation('visaReq5', 'Wanita di bawah 45 tahun wajib didampingi mahram (atau menggunakan visa privat tertentu).') },
    { text: getTranslation('visaReq6', 'Durasi tinggal maksimal 30 hari di Arab Saudi.') },
    { text: getTranslation('visaReq7', 'Jadwal visa aktif setelah tiket & hotel terkonfirmasi di sistem Saudi.') },
    { text: getTranslation('visaReq8', 'Penggunaan visa hanya untuk ibadah Umrah, tidak untuk bekerja atau menetap.') },
  ];

  return (
    <div className="bg-gray-800/50 p-8 rounded-2xl shadow-xl border border-gray-700 backdrop-blur-sm">
      <div className="flex items-center mb-6">
        <ShieldCheck className="w-10 h-10 text-amber-400 mr-4 flex-shrink-0" />
        <div>
          <h3 className="text-2xl font-bold text-white">
            {getTranslation('visaRequirementsTitle', 'Syarat Penerbitan Visa Umrah 1447 H')}
          </h3>
          <p className="text-gray-400">
            {getTranslation('visaRequirementsSubtitle', '(Mengacu pada regulasi terbaru Kementerian Haji dan Umrah Arab Saudi)')}
          </p>
        </div>
      </div>
      <ul className="space-y-3 list-decimal list-inside text-gray-300 pl-2">
        {requirements.map((req, index) => (
          <li key={index}>{req.text}</li>
        ))}
      </ul>
    </div>
  );
};

const PricingTables = () => {
  const [selectedCurrency, setSelectedCurrency] = useState('USD');
  const [activeTab, setActiveTab] = useState('la-b2b');
  const { translations, language } = useContext(LanguageContext);
  const { pricingPackages: pricingPlans, bundlingPackages, isLoading } = useContent();
  const getTranslation = (key, fallback) => translations[key] || fallback;

  const translatePackageData = (packages) => {
    if (!packages) return [];
    return packages.map(pkg => ({
      ...pkg,
      name: getTranslation(`pkg_${pkg.id}_name`, pkg.name),
      description: getTranslation(`pkg_${pkg.id}_desc`, pkg.description),
      includes: pkg.includes.map((item, index) => getTranslation(`pkg_${pkg.id}_inc_${index}`, item)),
      excludes: pkg.excludes.map((item, index) => getTranslation(`pkg_${pkg.id}_exc_${index}`, item)),
      notes: pkg.notes.map((item, index) => getTranslation(`pkg_${pkg.id}_note_${index}`, item)),
    }));
  };

  const translatedHandlingPackages = useMemo(() => translatePackageData(pricingPlans), [pricingPlans, language, translations]);
  const translatedBundlingPackages = useMemo(() => translatePackageData(bundlingPackages), [bundlingPackages, language, translations]);

  const allHandlingFeatures = useMemo(() => {
    if (!translatedHandlingPackages || translatedHandlingPackages.length === 0) return [];
    const features = new Set();
    translatedHandlingPackages.forEach(plan => {
        (plan.includes || []).forEach(feature => features.add(feature.split(':')[0].trim()));
        (plan.excludes || []).forEach(feature => features.add(feature.split(':')[0].trim()));
    });
    return Array.from(features);
  }, [translatedHandlingPackages]);

  const allBundlingFeatures = useMemo(() => {
    if (!translatedBundlingPackages || translatedBundlingPackages.length === 0) return [];
    const features = new Set();
    translatedBundlingPackages.forEach(plan => {
        (plan.includes || []).forEach(feature => features.add(feature.split(':')[0].trim()));
        (plan.excludes || []).forEach(feature => features.add(feature.split(':')[0].trim()));
    });
    return Array.from(features);
  }, [translatedBundlingPackages]);

  const handleTabChange = (value) => {
    setActiveTab(value);
    window.scrollTo(0, 0);
  };

  const pageTitles = {
    'la-b2b': {
      title: getTranslation('laB2bPackagesTitle', 'Penawaran Paket LA B2B'),
      description: getTranslation('laB2bPackagesSubtitle', 'Solusi Land Arrangement lengkap dan kompetitif untuk partner travel agent.')
    },
    'handling': {
      title: getTranslation('handlingPackagesTitle', 'Paket Handling Service'),
      description: getTranslation('handlingPackagesSubtitle', 'Layanan profesional untuk memastikan kenyamanan jamaah Anda selama di Tanah Suci.')
    },
    'bundling': {
      title: getTranslation('bundlingPackageTitle', 'Paket Bundling Visa + Handling Umrah'),
      description: getTranslation('bundlingPackageSubtitle', 'Solusi terintegrasi Visa Umrah resmi dan layanan handling lengkap untuk travel partner.')
    },
    'airport-handling': {
      title: getTranslation('airportHandlingTitle', 'Handling Airport Only (Arab Saudi)'),
      description: getTranslation('airportHandlingDesc', 'Layanan khusus untuk mendampingi jamaah Umrah saat proses kedatangan dan/atau kepulangan di bandara Arab Saudi.')
    },
    'faq': {
      title: getTranslation('pricingFaqTitle', 'Pertanyaan Umum Seputar Harga'),
      description: getTranslation('pricingFaqSubtitle', 'Temukan jawaban untuk pertanyaan yang paling sering diajukan mengenai paket dan harga kami.')
    }
  };

  if (isLoading) {
    return (
      <section className="py-24 bg-gradient-to-b from-background to-secondary">
        <PricingHeader />
        <div className="container mx-auto px-4 md:px-6 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mt-10"></div>
          <p className="text-white text-lg mt-4">{getTranslation('loadingPriceData', 'Memuat data harga...')}</p>
        </div>
      </section>
    );
  }

  return (
    <>
      <PricingHeader title={pageTitles[activeTab]?.title} description={pageTitles[activeTab]?.description} />
      <section className="py-24 bg-gradient-to-b from-secondary to-background">
        <div className="container mx-auto px-4 md:px-6">
          <div className="mb-12">
            <CurrencySelector selectedCurrency={selectedCurrency} onCurrencyChange={setSelectedCurrency} />
          </div>
          
          <Tabs defaultValue="la-b2b" className="w-full lg:flex lg:space-x-12 relative" onValueChange={handleTabChange}>
            <div className="w-full lg:w-1/4 xl:w-1/5 mb-8 lg:mb-0">
              <TabsList className="flex-col items-stretch h-auto bg-gray-800/50 p-3 rounded-xl border border-gray-700 space-y-2 lg:sticky lg:top-28">
                <TabsTrigger value="la-b2b" className="sidebar-tab-trigger">
                  <Briefcase className="w-5 h-5 mr-3"/>
                  <div className="text-left">
                    <p className="font-semibold">{getTranslation('laB2bTabTitle', 'Paket LA B2B')}</p>
                    <p className="text-xs opacity-70">{getTranslation('laB2bTabDesc', 'Penawaran untuk travel agent')}</p>
                  </div>
                </TabsTrigger>
                <TabsTrigger value="handling" className="sidebar-tab-trigger">
                  <Package className="w-5 h-5 mr-3"/>
                  <div className="text-left">
                    <p className="font-semibold">{getTranslation('handlingPackagesTitle', 'Paket Handling')}</p>
                    <p className="text-xs opacity-70">{getTranslation('handlingPackagesTabDesc', 'Layanan darat profesional')}</p>
                  </div>
                </TabsTrigger>
                <TabsTrigger value="bundling" className="sidebar-tab-trigger">
                  <Layers className="w-5 h-5 mr-3"/>
                  <div className="text-left">
                    <p className="font-semibold">{getTranslation('bundlingPackageTitleShort', 'Bundling Handling & Visa')}</p>
                    <p className="text-xs opacity-70">{getTranslation('bundlingPackagesTabDesc', 'Solusi lengkap visa & handling')}</p>
                  </div>
                </TabsTrigger>
                <TabsTrigger value="airport-handling" className="sidebar-tab-trigger">
                  <Plane className="w-5 h-5 mr-3"/>
                  <div className="text-left">
                    <p className="font-semibold">{getTranslation('airportHandlingOnlyTitle', 'Handling Airport Only')}</p>
                    <p className="text-xs opacity-70">{getTranslation('airportHandlingOnlyDesc', 'Layanan khusus bandara')}</p>
                  </div>
                </TabsTrigger>
                <TabsTrigger value="faq" className="sidebar-tab-trigger">
                  <Info className="w-5 h-5 mr-3"/>
                  <div className="text-left">
                    <p className="font-semibold">{getTranslation('pricingFaqTitleShort', 'Tanya Jawab')}</p>
                    <p className="text-xs opacity-70">{getTranslation('pricingFaqTabDesc', 'Pertanyaan umum seputar harga')}</p>
                  </div>
                </TabsTrigger>
              </TabsList>
            </div>
            
            <div className="w-full lg:w-3/4 xl:w-4/5">
              <TabsContent value="la-b2b">
                <LaB2bPackages selectedCurrency={selectedCurrency} />
              </TabsContent>

              <TabsContent value="handling">
                <PackageSection 
                    type="handling_service"
                    title={getTranslation('handlingPackagesTitle', 'Paket Handling Service')}
                    subtitle={getTranslation('handlingPackagesSubtitle', 'Layanan profesional untuk memastikan kenyamanan jamaah Anda selama di Tanah Suci.')}
                    plans={translatedHandlingPackages}
                    selectedCurrency={selectedCurrency}
                    allFeatures={allHandlingFeatures}
                />
              </TabsContent>

              <TabsContent value="bundling">
                <PackageSection 
                    type="bundling_visa_handling"
                    title={getTranslation('bundlingPackageTitle', 'Paket Bundling Visa + Handling Umrah')}
                    subtitle={getTranslation('bundlingPackageSubtitle', 'Solusi terintegrasi Visa Umrah resmi dan layanan handling lengkap untuk travel partner.')}
                    plans={translatedBundlingPackages}
                    selectedCurrency={selectedCurrency}
                    allFeatures={allBundlingFeatures}
                />
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                  className="my-24"
                >
                  <VisaRequirements />
                </motion.div>
              </TabsContent>

              <TabsContent value="airport-handling">
                <AirportHandlingOnly selectedCurrency={selectedCurrency} />
              </TabsContent>

              <TabsContent value="faq">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.1 }}
                >
                  <div className='text-center md:text-left'>
                    <h2 className='text-3xl md:text-4xl font-bold mb-4 text-white'>
                      {getTranslation('pricingFaqTitle', 'Pertanyaan Umum Seputar Harga')}
                    </h2>
                    <p className="text-lg text-gray-400 max-w-3xl mb-10">
                      {getTranslation('pricingFaqSubtitle', 'Temukan jawaban untuk pertanyaan yang paling sering diajukan mengenai paket dan harga kami.')}
                    </p>
                  </div>
                  <PricingFaq />
                </motion.div>
              </TabsContent>
            </div>
          </Tabs>
          
          <motion.div 
            initial={{ opacity:0, y:30 }}
            whileInView={{ opacity:1, y:0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.7, delay: 0.5, type: 'spring', stiffness:100 }}
            className="mt-24 text-center bg-gray-800/50 p-10 rounded-xl shadow-xl border border-gray-700 relative overflow-hidden"
          >
            <div className="absolute -top-10 -right-10 w-40 h-40 bg-gradient-to-br from-amber-500/20 to-transparent rounded-full blur-2xl"></div>
            <div className="absolute -bottom-10 -left-10 w-40 h-40 bg-gradient-to-tl from-amber-500/20 to-transparent rounded-full blur-2xl"></div>

            <MessageSquare className="w-12 h-12 mx-auto mb-5 text-[#FFD700]" />
            <h3 className="text-3xl font-bold text-white mb-4">
              {getTranslation('needCustomization', 'Butuh Paket LA B2B atau Layanan Lain?')}
            </h3>
            <p className="text-gray-300 mb-8 text-lg max-w-2xl mx-auto leading-relaxed">
              {getTranslation('customizationText', 'Kami menyediakan paket lengkap LA B2B, visa processing, dan layanan komprehensif lainnya. Hubungi kami untuk penawaran yang dipersonalisasi sesuai kebutuhan travel agent Anda.')}
            </p>
            <div className='flex flex-col sm:flex-row justify-center items-center gap-4'>
                <Button 
                    asChild
                    variant="outline" 
                    className="border-[#FFD700] text-[#FFD700] hover:bg-[#FFD700] hover:text-black text-lg px-8 py-3.5 font-semibold transition-all duration-300 ease-in-out transform hover:scale-105 group"
                >
                <a href={generateSpecialOfferLink("Tim Arrahmah", language, getTranslation)} target="_blank" rel="noopener noreferrer">
                    {getTranslation('requestSpecialOffer', 'Minta Penawaran Lengkap')}
                    <MessageSquare className="ml-2.5 h-5 w-5 transition-transform duration-300 group-hover:translate-x-1" />
                </a>
                </Button>
                <Button asChild variant="link" className="text-[#FFD700] hover:text-amber-300 text-lg font-medium group">
                    <Link to="/services">
                        {getTranslation('seeAllServices', "Lihat Semua Layanan")}
                        <ChevronsRight className="ml-2 h-5 w-5 transition-transform duration-300 group-hover:translate-x-1"/>
                    </Link>
                </Button>
            </div>
          </motion.div>
        </div>
      </section>
    </>
  );
};

export default PricingTables;
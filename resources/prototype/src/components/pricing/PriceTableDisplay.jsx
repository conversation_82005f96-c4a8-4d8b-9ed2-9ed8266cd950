import React, { useContext } from 'react';
import { cn } from '@/lib/utils';
import { LanguageContext } from '@/contexts/LanguageContext';
import { useCurrency } from '@/contexts/CurrencyContext';
import { convertCurrency } from '@/utils/currencyConverter';
import CurrencyDisplay from '@/components/pricing/CurrencyDisplay';

const PriceTableDisplay = ({ plans, selectedCurrency }) => {
  const { translations, language } = useContext(LanguageContext);
  const { rates } = useCurrency();

  if (!plans || plans.length === 0) {
    return (
      <div className="text-center p-8 text-gray-400">
        Informasi harga tidak tersedia saat ini.
      </div>
    );
  }

  const referencePaxTiers = plans.find(p => p.paxPricing && p.paxPricing.length > 0)?.paxPricing || [];

  if (referencePaxTiers.length === 0) {
    return (
      <div className="text-center p-8 text-gray-400">
        Struktur harga tidak tersedia untuk paket yang ada.
      </div>
    );
  }

  return (
    <div className="overflow-x-auto shadow-2xl rounded-xl bg-gradient-to-br from-gray-800 via-gray-900 to-black border border-gray-700">
      <table className="w-full min-w-[700px] text-left border-collapse">
        <thead>
          <tr className="border-b border-gray-700">
            <th className="p-5 font-semibold text-sm text-gray-300 uppercase tracking-wider">{translations.totalPax || 'Total Pax'}</th>
            {plans.map(plan => (
              <th key={plan.name} className={cn(
                "p-5 font-semibold text-sm text-center uppercase tracking-wider",
                plan.popular ? 'text-amber-400' : 'text-white'
              )}>
                {plan.name}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {referencePaxTiers.map((paxTier) => (
            <tr key={paxTier.pax} className="border-b border-gray-700 last:border-b-0 hover:bg-gray-800/60 transition-colors duration-200 ease-in-out">
              <td className="p-4 text-gray-200 font-medium">{paxTier.pax} Pax</td>
              {plans.map(plan => {
                const currentTierPrice = plan.paxPricing?.find(p => p.pax === paxTier.pax)?.price;
                const convertedPrice = currentTierPrice !== undefined ? convertCurrency(currentTierPrice, 'USD', selectedCurrency, rates) : null;
                
                return (
                  <td key={`${plan.name}-${paxTier.pax}`} className={cn(
                    "p-4 text-center font-semibold",
                    plan.popular ? 'text-amber-300' : 'text-gray-200'
                  )}>
                    <CurrencyDisplay amount={convertedPrice} currency={selectedCurrency} lang={language} />
                  </td>
                );
              })}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default PriceTableDisplay;
import React, { useContext } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { LanguageContext } from '@/contexts/LanguageContext';
import { Star, MapPin, CheckCircle2, XCircle, ShoppingCart, Users, ChevronDown, BedDouble } from 'lucide-react';
import { useCurrency } from '@/contexts/CurrencyContext';
import { convertCurrency } from '@/utils/currencyConverter';
import CurrencyDisplay from '@/components/pricing/CurrencyDisplay';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { useNavigate } from 'react-router-dom';

const LaB2bCard = ({ pkg, selectedCurrency, index }) => {
    const { translations, language } = useContext(LanguageContext);
    const { rates } = useCurrency();
    const navigate = useNavigate();
    const getTranslation = (key, fallback) => translations[key] || fallback;

    const handleOrder = () => {
        navigate('/order', { state: { selectedPackageId: pkg.id } });
    };

    const containerVariants = {
        hidden: { opacity: 0, y: 50 },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                duration: 0.5,
                delay: index * 0.1,
                type: 'spring',
                stiffness: 80
            }
        }
    };
    
    const translatedPackageName = getTranslation(pkg.nameKey, pkg.name);
    
    const startingPrice = pkg.pricing.length > 0 ? Math.min(...pkg.pricing.map(p => p.quad)) : 0;
    const convertedStartingPrice = convertCurrency(startingPrice, 'USD', selectedCurrency, rates);

    return (
        <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.2 }}
        >
            <Card className="bg-gradient-to-br from-gray-800/60 to-gray-900/80 border border-gray-700/60 rounded-3xl shadow-2xl overflow-hidden flex flex-col backdrop-blur-sm h-full group">
                <CardHeader className="p-6">
                    <div className="flex justify-between items-start mb-2">
                        <CardTitle className="text-2xl font-bold text-white tracking-tight pr-4">{translatedPackageName}</CardTitle>
                        <div className="flex-shrink-0 flex items-center space-x-1 bg-gray-900/50 px-3 py-1.5 rounded-full border border-gray-700">
                            {[...Array(pkg.stars)].map((_, i) => (
                                <Star key={i} className="w-4 h-4 text-amber-400 fill-current" />
                            ))}
                        </div>
                    </div>
                </CardHeader>
                <CardContent className="p-6 pt-0 flex-grow flex flex-col">
                    <div className="space-y-4 mb-6">
                        <div className="bg-black/30 p-4 rounded-xl flex items-center">
                            <MapPin size={20} className="mr-4 text-gray-400 flex-shrink-0"/>
                            <div>
                                <p className="text-sm text-gray-400">{getTranslation('madinah', 'Madinah')}</p>
                                <p className="font-semibold text-white">{pkg.hotelMadinah}</p>
                            </div>
                        </div>
                        <div className="bg-black/30 p-4 rounded-xl flex items-center">
                            <MapPin size={20} className="mr-4 text-gray-400 flex-shrink-0"/>
                            <div>
                                <p className="text-sm text-gray-400">{getTranslation('makkah', 'Makkah')}</p>
                                <p className="font-semibold text-white">{pkg.hotelMakkah}</p>
                            </div>
                        </div>
                    </div>

                    <div className="mt-auto pt-6">
                        <div className="text-center mb-6">
                            <p className="text-gray-400 text-sm mb-1">{getTranslation('startingFrom', 'Mulai dari')}</p>
                            <div className="text-4xl font-bold text-white tracking-tight">
                                <CurrencyDisplay amount={convertedStartingPrice} currency={selectedCurrency} lang={language} isLarge={true} />
                            </div>
                             <p className="text-gray-400 text-xs mt-1">/ {getTranslation('pax', 'PAX')}</p>
                        </div>
                        
                        <Accordion type="single" collapsible className="w-full">
                            <AccordionItem value="details" className="border-none">
                                <AccordionTrigger className="text-sm text-amber-400 hover:no-underline justify-center font-semibold">
                                     {getTranslation('viewDetails', 'Lihat Rincian')}
                                     <ChevronDown className="h-4 w-4 shrink-0 transition-transform duration-200" />
                                </AccordionTrigger>
                                <AccordionContent className="pt-6">
                                    <div className="space-y-6">
                                        <div className="bg-black/30 rounded-xl p-4">
                                            <div className="grid grid-cols-4 gap-x-2 text-xs text-gray-400 font-bold uppercase tracking-wider mb-3 text-center">
                                                <span className="text-left flex items-center"><Users size={12} className="mr-1.5"/>{getTranslation('pax', 'PAX')}</span>
                                                <span className="flex items-center justify-center"><BedDouble size={12} className="mr-1.5"/>{getTranslation('quad', 'QUAD')}</span>
                                                <span className="flex items-center justify-center"><BedDouble size={12} className="mr-1.5"/>{getTranslation('triple', 'TRIPLE')}</span>
                                                <span className="flex items-center justify-center"><BedDouble size={12} className="mr-1.5"/>{getTranslation('double', 'DOUBLE')}</span>
                                            </div>
                                            <div className="space-y-2">
                                                {pkg.pricing.map((tier, i) => (
                                                <div key={i} className="grid grid-cols-4 gap-x-2 items-center py-2 border-b border-white/10 last:border-b-0">
                                                    <div className="font-medium text-gray-300 flex items-center text-sm">{tier.pax}</div>
                                                    <CurrencyDisplay amount={convertCurrency(tier.quad, 'USD', selectedCurrency, rates)} currency={selectedCurrency} lang={language} />
                                                    <CurrencyDisplay amount={convertCurrency(tier.triple, 'USD', selectedCurrency, rates)} currency={selectedCurrency} lang={language} />
                                                    <CurrencyDisplay amount={convertCurrency(tier.double, 'USD', selectedCurrency, rates)} currency={selectedCurrency} lang={language} />
                                                </div>
                                                ))}
                                            </div>
                                        </div>
                                        <div>
                                            <h4 className="font-semibold text-green-400 mb-3 flex items-center text-base"><CheckCircle2 size={18} className="mr-2"/> {getTranslation('includes', 'Termasuk')}</h4>
                                            <ul className="space-y-2">
                                            {pkg.includes.map((item, i) => (
                                                <li key={i} className="flex items-start text-sm">
                                                    <CheckCircle2 className="w-4 h-4 mr-2.5 mt-0.5 flex-shrink-0 text-green-400/80" />
                                                    <span className="text-gray-300">{getTranslation(item.key, item.default)}</span>
                                                </li>
                                            ))}
                                            </ul>
                                        </div>
                                        <div>
                                            <h4 className="font-semibold text-red-400 mb-3 flex items-center text-base"><XCircle size={18} className="mr-2"/> {getTranslation('excludes', 'Tidak Termasuk')}</h4>
                                            <ul className="space-y-2">
                                            {pkg.excludes.map((item, i) => (
                                                 <li key={i} className="flex items-start text-sm">
                                                    <XCircle className="w-4 h-4 mr-2.5 mt-0.5 flex-shrink-0 text-red-400/80" />
                                                    <span className="text-gray-300">{getTranslation(item.key, item.default)}</span>
                                                </li>
                                            ))}
                                            </ul>
                                        </div>
                                    </div>
                                </AccordionContent>
                            </AccordionItem>
                        </Accordion>

                        <Button onClick={handleOrder} className="w-full gold-gradient text-black font-bold text-base py-3 mt-6 hover:opacity-90 transition-opacity transform hover:scale-105 duration-300">
                            <ShoppingCart size={18} className="mr-2.5"/> {getTranslation('contactForBooking', 'Pesan Paket Ini')}
                        </Button>
                    </div>
                </CardContent>
            </Card>
        </motion.div>
    );
};

export default LaB2bCard;
const WHATSAPP_NUMBER = '6281289552018';

export const generateWhatsAppCustomLink = (language, getTranslation) => {
    const contactName = getTranslation('arrahmahTeam', '<PERSON>');
    let greeting;
    if (language === 'ar') {
        greeting = `${getTranslation('greeting_ar', 'السلام عليكم')} ${contactName}`;
    } else if (language === 'en') {
        greeting = `${getTranslation('greeting_en', 'Hello')} ${contactName}`;
    } else {
        greeting = `${getTranslation('greeting_id', 'Assalamu\'alaikum')} ${contactName}`;
    }
    
    const messageText = 
        language === 'ar' ? getTranslation('wa_custom_b2b_ar', '،\n\nأرغب في طلب باقة LA B2B مخصصة. هل يمكنكم مساعدتي؟\n\n(تم إرسال الرسالة من الموقع الإلكتروني)') :
        language === 'en' ? getTranslation('wa_custom_b2b_en', ',\n\nI would like to request a custom LA B2B package. Can you assist me?\n\n(message sent from website)') :
        getTranslation('wa_custom_b2b_id', ',\n\nSaya ingin meminta paket LA B2B custom. Bisakah dibantu?\n\n(pesan dikirim dari website)');

    const message = encodeURIComponent(`${greeting}${messageText}`);
    return `https://wa.me/${WHATSAPP_NUMBER}?text=${message}`;
};
import React, { useContext } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { LanguageContext } from '@/contexts/LanguageContext';
import { useCurrency } from '@/contexts/CurrencyContext';
import { convertCurrency } from '@/utils/currencyConverter';
import CurrencyDisplay from '@/components/pricing/CurrencyDisplay';
import { Check, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Star } from 'lucide-react';

const WHATSAPP_NUMBER = '6281289552018';

const generateWhatsAppLink = (packageName, contactName = "Customer Service", lang) => {
    let greeting;
    if (lang === 'ar') {
        greeting = `السلام عليكم ${contactName}`;
    } else if (lang === 'en') {
        greeting = `Hello ${contactName}`;
    } else {
        greeting = `Assalamu'alaikum ${contactName}`;
    }

    const messageText = 
    lang === 'ar' ? `،\n\nأنا مهتم بالباقة ${packageName}. هل يمكنني الحصول على مزinforid informasi?\n\n(تم إرسال الرسالة من الموقع الإلكتروني)` :
    lang === 'en' ? `,\n\nI am interested in the ${packageName} package. Can I get more information?\n\n(message sent from website)` :
    `,\n\nSaya tertarik dengan paket ${packageName}. Bisakah saya mendapatkan informasi lebih lanjut?\n\n(pesan dikirim dari website)`;

    const message = encodeURIComponent(`${greeting}${messageText}`);
    return `https://wa.me/${WHATSAPP_NUMBER}?text=${message}`;
};

const PlanComparisonTable = ({ plans, selectedCurrency, allFeatures }) => {
    const { translations, language } = useContext(LanguageContext);
    const { rates } = useCurrency();

    const referencePaxTiers = React.useMemo(() => {
        if (!plans || plans.length === 0) return [];
        const firstPlanWithPricing = plans.find(p => p.paxPricing && p.paxPricing.length > 0);
        return firstPlanWithPricing ? firstPlanWithPricing.paxPricing.map(p => p.pax) : [];
    }, [plans]);


    if (!plans || plans.length === 0) {
        return (
            <div className="text-center p-8 text-gray-400">
                {translations.pricingInfoNotAvailable || 'Informasi harga tidak tersedia saat ini.'}
            </div>
        );
    }
    
    return (
        <div className="overflow-x-auto shadow-2xl rounded-xl bg-gradient-to-br from-gray-800 via-gray-900 to-black border border-gray-700/80">
            <table className="w-full min-w-[800px] text-left border-collapse">
                <thead>
                    <tr className="border-b border-gray-700">
                        <th className="p-6 font-semibold text-base text-gray-300 uppercase tracking-wider sticky left-0 bg-gray-900/80 backdrop-blur-sm z-10">
                            {translations.feature || 'Paket'}
                        </th>
                        {plans.map(plan => (
                            <th key={plan.id} className={cn(
                                "p-6 font-bold text-lg text-center uppercase tracking-wider relative", 
                                plan.popular ? 'text-amber-400' : 'text-white'
                            )}>
                                {plan.popular && <Star className="w-4 h-4 absolute top-3 right-3 text-amber-400 fill-current" />}
                                {plan.name}
                            </th>
                        ))}
                    </tr>
                </thead>
                <tbody>
                    <tr className="border-b border-gray-700">
                         <td className="p-6 font-medium text-gray-200 sticky left-0 bg-gray-900/80 backdrop-blur-sm z-10"></td>
                         {plans.map(plan => (
                            <td key={`${plan.id}-cta`} className="p-6 text-center">
                                <Button asChild className={cn('w-full', plan.popular ? plan.popularButtonClass : plan.buttonClass)}>
                                    <a href={generateWhatsAppLink(plan.name, "Tim Arrahmah", language)} target="_blank" rel="noopener noreferrer">
                                        {translations.selectPackage || 'Pilih Paket'}
                                    </a>
                                </Button>
                            </td>
                         ))}
                    </tr>
                    
                    <tr className="border-b border-gray-700 bg-gray-800/20">
                        <td colSpan={plans.length + 1} className="p-3 font-semibold text-sm text-gray-300 uppercase tracking-wider text-center">
                            {translations.pricePerPax || 'Harga per Pax'}
                        </td>
                    </tr>

                    {referencePaxTiers.map(paxTier => (
                        <tr key={paxTier} className="border-b border-gray-700 hover:bg-gray-800/40 transition-colors duration-200 ease-in-out">
                            <td className="p-5 text-gray-300 font-medium sticky left-0 bg-gray-900/80 backdrop-blur-sm z-10">{paxTier} Pax</td>
                            {plans.map(plan => {
                                const tierPriceData = plan.paxPricing.find(p => p.pax === paxTier);
                                const price = tierPriceData ? convertCurrency(tierPriceData.price, 'USD', selectedCurrency, rates) : null;
                                return (
                                    <td key={`${plan.id}-${paxTier}`} className={cn("p-5 text-center font-semibold", plan.popular ? 'text-amber-300' : 'text-gray-200')}>
                                        <CurrencyDisplay amount={price} currency={selectedCurrency} lang={language} />
                                    </td>
                                );
                            })}
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
};

export default PlanComparisonTable;
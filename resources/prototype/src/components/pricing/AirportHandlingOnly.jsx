import React, { useContext } from 'react';
import { motion } from 'framer-motion';
import { CheckCircle, AlertTriangle, Info, ShoppingCart } from 'lucide-react';
import { LanguageContext } from '@/contexts/LanguageContext';
import { useCurrency } from '@/contexts/CurrencyContext';
import { convertCurrency } from '@/utils/currencyConverter';
import CurrencyDisplay from '@/components/pricing/CurrencyDisplay';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';

const AirportHandlingOnly = ({ selectedCurrency }) => {
  const { translations, language } = useContext(LanguageContext);
  const { rates } = useCurrency();
  const navigate = useNavigate();
  const getTranslation = (key, fallback) => translations[key] || fallback;

  const handleOrder = () => {
    const orderDetails = {
        packageType: 'handling_airport_only',
        airportService: 'JED/MED - Paket PP'
    };
    navigate('/order', { state: { orderDetails } });
  };

  const pricingData = [
    {
      airportKey: 'airportIntl',
      defaultAirport: 'Internasional (JED/MED)',
      arrival: 19,
      departure: 20,
      roundTrip: 33,
      snack: 1,
    },
    {
      airportKey: 'airportHajj',
      defaultAirport: 'Terminal Haji',
      arrival: 16,
      departure: 24,
      roundTrip: 35,
      snack: 1,
    },
  ];

  const includedServices = [
    getTranslation('includedService1', 'Tim handling profesional saat kedatangan & kepulangan.'),
    getTranslation('includedService2', 'Koordinator lapangan untuk porter dan bagasi.'),
    getTranslation('includedService3', 'Biaya porter bagasi sudah termasuk.'),
    getTranslation('includedService4', 'Bantuan proses check-in dan boarding saat kepulangan.'),
    getTranslation('includedService5', 'Welcome drink air zamzam & kurma di atas bus.'),
    getTranslation('includedService6', 'Nasi box saat kedatangan & kepulangan.'),
    getTranslation('includedService7', 'Snack premium atau Al Baik (opsional sesuai permintaan).'),
    getTranslation('includedService8', 'Free Air Zamzam / Pax saat kepulangan.'),
  ];

  const termsAndNotes = [
    getTranslation('term1', 'Harga berlaku untuk minimal 35 pax per group.'),
    getTranslation('term2', 'Group dengan jumlah di bawah 35 pax akan dikenakan penyesuaian harga.'),
    getTranslation('term3', 'Kelebihan bagasi saat kepulangan menjadi tanggung jawab travel.'),
    getTranslation('term4', 'Jamaah harus tiba di bandara minimal 4 jam sebelum keberangkatan.'),
    getTranslation('term5', 'Keterlambatan oleh pihak maskapai atau hal teknis bandara di luar tanggung jawab tim handling.'),
  ];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5, delay: 0.1 }}
    >
      <div className="text-center md:text-left mb-12">
        <h2 className="text-3xl md:text-4xl font-bold text-white">
          {getTranslation('airportHandlingTitle', 'Handling Airport Only (Arab Saudi)')}
        </h2>
        <p className="text-lg text-amber-400 font-semibold mt-1">
          {getTranslation('airportHandlingSeason', 'Musim Umrah 1447 H')}
        </p>
        <p className="text-lg text-gray-400 max-w-3xl mt-2">
          {getTranslation('airportHandlingDesc', 'Layanan khusus untuk mendampingi jamaah Umrah saat proses kedatangan dan/atau kepulangan di bandara Arab Saudi.')}
        </p>
      </div>

      <div className="mb-16">
        <h3 className="text-2xl font-bold text-white mb-2 text-center md:text-left">
          {getTranslation('serviceOptionsPrice', 'Pilihan Layanan & Harga')}
        </h3>
        <p className="text-gray-400 mb-6 text-center md:text-left">
          {getTranslation('serviceOptionsGroupSize', '(Khusus untuk Group 35–48 Pax)')}
        </p>
        <div className="overflow-x-auto shadow-2xl rounded-xl bg-gradient-to-br from-gray-800 via-gray-900 to-black border border-gray-700/80">
          <table className="w-full min-w-[700px] text-left border-collapse">
            <thead className="bg-gray-900/50">
              <tr className="border-b border-gray-700">
                <th className="p-5 font-semibold text-sm text-gray-300 uppercase tracking-wider">{getTranslation('airport', 'Bandara')}</th>
                <th className="p-5 font-semibold text-sm text-center text-gray-300 uppercase tracking-wider">{getTranslation('arrival', 'Kedatangan')}</th>
                <th className="p-5 font-semibold text-sm text-center text-gray-300 uppercase tracking-wider">{getTranslation('departure', 'Kepulangan')}</th>
                <th className="p-5 font-semibold text-sm text-center text-amber-400 uppercase tracking-wider">{getTranslation('roundTripPackage', 'Paket PP')}</th>
                <th className="p-5 font-semibold text-sm text-center text-gray-300 uppercase tracking-wider">{getTranslation('alBaikSnack', 'Al Baik (Opsional)')}</th>
              </tr>
            </thead>
            <tbody>
              {pricingData.map((row, index) => (
                <tr key={index} className="border-b border-gray-700 last:border-b-0 hover:bg-gray-800/60 transition-colors duration-200 ease-in-out">
                  <td className="p-4 text-gray-200 font-medium">{getTranslation(row.airportKey, row.defaultAirport)}</td>
                  <td className="p-4 text-center text-gray-200 font-semibold">
                    <CurrencyDisplay amount={convertCurrency(row.arrival, 'USD', selectedCurrency, rates)} currency={selectedCurrency} lang={language} />
                  </td>
                  <td className="p-4 text-center text-gray-200 font-semibold">
                    <CurrencyDisplay amount={convertCurrency(row.departure, 'USD', selectedCurrency, rates)} currency={selectedCurrency} lang={language} />
                  </td>
                  <td className="p-4 text-center text-amber-300 font-bold">
                    <CurrencyDisplay amount={convertCurrency(row.roundTrip, 'USD', selectedCurrency, rates)} currency={selectedCurrency} lang={language} />
                  </td>
                  <td className="p-4 text-center text-gray-200 font-semibold">
                    + <CurrencyDisplay amount={convertCurrency(row.snack, 'USD', selectedCurrency, rates)} currency={selectedCurrency} lang={language} /> / pax
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        <div className="mt-4 text-center md:text-left">
          <p className="text-amber-400 text-sm flex items-center justify-center md:justify-start">
            <Info className="w-4 h-4 mr-2" />
            {getTranslation('roundTripNote', 'Paket PP (Pulang–Pergi) = harga khusus jika mengambil layanan kedatangan & kepulangan sekaligus.')}
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
        <div className="bg-gray-800/50 p-8 rounded-2xl shadow-xl border border-gray-700">
          <h3 className="text-xl font-bold text-white mb-6 flex items-center">
            <CheckCircle className="w-6 h-6 mr-3 text-green-400" />
            {getTranslation('facilitiesIncluded', 'FASILITAS & LAYANAN TERMASUK')}
          </h3>
          <ul className="space-y-3 text-gray-300">
            {includedServices.map((service, index) => (
              <li key={index} className="flex items-start">
                <span className="text-green-400 font-bold mr-2">✓</span>
                <span>{service}</span>
              </li>
            ))}
          </ul>
        </div>
        <div className="bg-gray-800/50 p-8 rounded-2xl shadow-xl border border-gray-700">
          <h3 className="text-xl font-bold text-white mb-6 flex items-center">
            <AlertTriangle className="w-6 h-6 mr-3 text-yellow-400" />
            {getTranslation('termsAndNotes', 'KETENTUAN & CATATAN PENTING')}
          </h3>
          <ul className="space-y-3 text-gray-300">
            {termsAndNotes.map((note, index) => (
              <li key={index} className="flex items-start">
                <span className="text-yellow-400 font-bold mr-2">!</span>
                <span>{note}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>
      <div className="mt-12 text-center">
        <Button onClick={handleOrder} size="lg" className="gold-gradient text-black font-bold px-8 py-3.5 group text-base transform hover:scale-105 duration-300">
            <ShoppingCart className="mr-2.5 h-5 w-5" /> {getTranslation('orderAirportHandling', 'Pesan Layanan Handling Airport')}
        </Button>
      </div>
    </motion.div>
  );
};

export default AirportHandlingOnly;
import React, { useContext } from 'react';
import { motion } from 'framer-motion';
import { LanguageContext } from '@/contexts/LanguageContext.jsx';
import { CheckCircle, Hotel, Bus, UtensilsCrossed } from 'lucide-react';

const WhatIsLA = () => {
  const { translations } = useContext(LanguageContext);

  const getTranslation = (key, fallback) => translations[key] || fallback;

  const services = [
    { icon: <Hotel className="w-8 h-8 text-primary" />, text: getTranslation('laServiceHotel', 'Akomodasi Hotel') },
    { icon: <Bus className="w-8 h-8 text-primary" />, text: getTranslation('laServiceTransport', 'Transportasi Bus') },
    { icon: <UtensilsCrossed className="w-8 h-8 text-primary" />, text: getTranslation('laServiceCatering', 'Katering & Konsumsi') },
  ];

  return (
    <section className="py-20 bg-background text-white">
      <div className="container mx-auto px-4 md:px-6">
        <div className="grid md:grid-cols-2 gap-12 items-center">
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true, amount: 0.5 }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
          >
            <img 
              className="rounded-xl shadow-2xl w-full h-auto object-cover"
              alt={getTranslation('laImageAlt', 'Jamaah umrah berfoto bersama dengan latar belakang Ka\'bah')}
             src="https://umrahype.com//storage/65/01JC3W21WB5PTGTF5J9NP398GB.jpeg" />
          </motion.div>
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true, amount: 0.5 }}
            transition={{ duration: 0.8, ease: 'easeOut', delay: 0.2 }}
          >
            <h2 className="text-3xl md:text-4xl font-extrabold mb-6 tracking-tight">
              {getTranslation('laTitle', 'Apa Itu Land Arrangement Umrah?')}
            </h2>
            <p className="text-lg text-gray-300 mb-6 leading-relaxed">
              {getTranslation('laDescription', 'Land Arrangement (LA) adalah inti dari jasa handling umrah. Ini mencakup semua persiapan dan pengelolaan layanan darat di Arab Saudi, memastikan perjalanan jamaah Anda berjalan lancar dari kedatangan hingga kepulangan. Kami mengurus semua detailnya agar Anda bisa fokus pada jamaah.')}
            </p>
            <div className="space-y-4">
              {services.map((service, index) => (
                <div key={index} className="flex items-center p-4 bg-gray-800/50 rounded-lg">
                  {service.icon}
                  <span className="ml-4 text-lg font-medium">{service.text}</span>
                </div>
              ))}
            </div>
            <p className="text-gray-400 mt-6 italic">
              {getTranslation('laBenefit', 'Dengan land arrangement umroh yang solid, travel Anda mendapatkan reputasi, dan jamaah mendapatkan ketenangan.')}
            </p>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default WhatIsLA;
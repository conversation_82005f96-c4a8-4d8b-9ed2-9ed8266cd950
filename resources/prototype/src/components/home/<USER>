import React, { useContext, useMemo } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { <PERSON>, CardFooter, CardHeader, CardTitle, CardDescription } from '@/components/ui/card.jsx';
import { Button } from '@/components/ui/button.jsx';
import { ArrowRight, CalendarDays, UserCircle } from 'lucide-react';
import { LanguageContext } from '@/contexts/LanguageContext.jsx';
import { useBlog } from '@/contexts/BlogContext.jsx';

const placeholderImage = "https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/0ef586d5607ce8348b025632bfe2a445.jpg"; 

const BlogSection = () => {
  const { posts, isLoading } = useBlog();
  const { translations } = useContext(LanguageContext);
  
  const getTranslation = (key, fallback) => translations[key] || fallback;

  const latestPosts = useMemo(() => {
    return posts
      .filter(post => post.status === 'Published')
      .sort((a, b) => new Date(b.publishDate || b.createdAt) - new Date(a.publishDate || a.createdAt))
      .slice(0, 3);
  }, [posts]);

  const formatDate = (dateString) => {
    if (!dateString) return getTranslation('dateNotAvailable', 'Tanggal tidak tersedia');
    try {
      return new Date(dateString).toLocaleDateString(getTranslation('locale', 'id-ID'), {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
    } catch (error) {
      return getTranslation('invalidDateFormat', 'Format tanggal salah');
    }
  };
  
  const getExcerpt = (htmlContent, maxLength = 120) => {
    if (!htmlContent) return getTranslation('noDescriptionAvailable', "Deskripsi tidak tersedia.");
    const textContent = htmlContent.replace(/<[^>]+>/g, '');
    if (textContent.length <= maxLength) return textContent;
    return textContent.substring(0, maxLength) + '...';
  };


  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: { staggerChildren: 0.2, delayChildren: 0.2 },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30, scale: 0.95 },
    visible: { 
      opacity: 1, 
      y: 0, 
      scale: 1,
      transition: { type: "spring", stiffness: 100, damping: 12 }
    },
  };

  return (
    <section className="py-20 bg-secondary">
      <div className="container mx-auto px-4 md:px-6">
        <motion.div
          initial={{ opacity: 0, y: -30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.5 }}
          transition={{ duration: 0.7, type: "spring", stiffness: 100 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-extrabold text-white mb-4 tracking-tight">
            {getTranslation('blogSectionTitle', "Wawasan & Artikel Handling Umroh")}
          </h2>
          <p className="text-lg md:text-xl text-gray-400 max-w-2xl mx-auto">
            {getTranslation('blogSectionSubtitle', "Dapatkan informasi terbaru seputar jasa handling umrah dan land arrangement dari para ahli.")}
          </p>
          <div className="w-24 h-1.5 bg-gradient-to-r from-[#FFD700] to-[#FFA500] mx-auto rounded-full mt-4"></div>
        </motion.div>

        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
        >
          {isLoading ? (
            Array.from({ length: 3 }).map((_, index) => (
              <motion.div key={index} variants={itemVariants}>
                <Card className="bg-gradient-to-br from-gray-800 to-gray-900 border border-gray-700/50 h-full rounded-xl p-6">
                  <div className="animate-pulse flex flex-col h-full">
                    <div className="bg-gray-700 h-40 w-full rounded-md mb-4"></div>
                    <div className="h-6 bg-gray-700 rounded w-3/4 mb-2"></div>
                    <div className="h-4 bg-gray-700 rounded w-1/2 mb-4"></div>
                    <div className="h-4 bg-gray-700 rounded w-full mb-2"></div>
                    <div className="h-4 bg-gray-700 rounded w-full mb-2"></div>
                    <div className="h-4 bg-gray-700 rounded w-5/6"></div>
                  </div>
                </Card>
              </motion.div>
            ))
          ) : latestPosts.length > 0 ? (
            latestPosts.map((post) => (
              <motion.div key={post.id} variants={itemVariants}>
                <Card className="bg-gradient-to-br from-gray-800 to-gray-900 border border-gray-700/50 text-white shadow-xl hover:shadow-glow transition-all duration-300 ease-in-out transform hover:-translate-y-1 flex flex-col h-full overflow-hidden rounded-xl group">
                  <CardHeader className="p-0">
                    <div className="aspect-video overflow-hidden">
                       <img
                        src={post.featured_image || placeholderImage}
                        alt={getTranslation('blogImageAlt', 'Gambar artikel blog tentang') + ` ${post.title}`}
                        className="w-full h-full object-cover transition-transform duration-500 ease-in-out group-hover:scale-105"
                        loading="lazy"
                      />
                    </div>
                    <div className="p-6">
                      <CardTitle className="text-2xl font-semibold text-primary mb-2 leading-tight min-h-[3em] line-clamp-2" title={post.title}>
                        {post.title}
                      </CardTitle>
                      <div className="flex items-center text-xs text-gray-400 space-x-3 mb-3">
                        <span className="flex items-center"><CalendarDays size={14} className="mr-1.5" /> {formatDate(post.publishDate)}</span>
                        <span className="flex items-center"><UserCircle size={14} className="mr-1.5" /> {post.author || "Admin"}</span>
                      </div>
                      <CardDescription className="text-gray-300 line-clamp-3 text-sm leading-relaxed min-h-[4.5em]">
                        {post.metaDescription || getExcerpt(post.content) }
                      </CardDescription>
                    </div>
                  </CardHeader>
                  <CardFooter className="mt-auto p-6 border-t border-gray-700/50">
                    <Link to={`/blog/${post.slug}`} className="w-full">
                      <Button variant="outline" className="w-full text-primary border-primary hover:bg-primary hover:text-black transition-colors duration-300 group">
                        {getTranslation('readMore', "Baca Selengkapnya")} <ArrowRight size={18} className="ml-2 transition-transform duration-300 group-hover:translate-x-1" />
                      </Button>
                    </Link>
                  </CardFooter>
                </Card>
              </motion.div>
            ))
          ) : (
            <motion.div variants={itemVariants} className="col-span-1 md:col-span-2 lg:col-span-3 text-center py-16">
              <p className="text-gray-400 text-lg">Belum ada artikel yang dipublikasikan. Silakan cek kembali nanti!</p>
            </motion.div>
          )}
        </motion.div>

        {latestPosts.length > 0 && (
          <motion.div 
            className="text-center mt-16"
            initial={{ opacity: 0, y:20 }}
            whileInView={{ opacity: 1, y:0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay:0.3 }}
          >
            <Button asChild variant="link" className="text-[#FFD700] hover:text-amber-300 text-lg font-medium group">
              <Link to="/blog">{getTranslation('viewAllPosts', "Lihat Semua Postingan")} <ArrowRight className="ml-2 h-5 w-5 transition-transform duration-300 group-hover:translate-x-1"/></Link>
            </Button>
          </motion.div>
        )}
      </div>
    </section>
  );
};

export default BlogSection;
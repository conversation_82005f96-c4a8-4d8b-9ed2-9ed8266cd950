import React, { useContext } from 'react';
import { motion } from 'framer-motion';
import { Instagram } from 'lucide-react';
import { LanguageContext } from '@/contexts/LanguageContext';

const InstagramFeed = () => {
  const { translations } = useContext(LanguageContext);
  const getTranslation = (key, fallback) => translations[key] || fallback;

  return (
    <section className="py-20 bg-background">
      <div className="container mx-auto px-4 md:px-6">
        <div className="text-center">
          <motion.h2 
            className="gradient-text mb-4 text-3xl md:text-4xl font-bold"
            initial={{ opacity: 0, y: -20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            {getTranslation('instagramTitle', 'Terhubung dengan <PERSON> di Instagram')}
          </motion.h2>
          <motion.p 
            className="text-gray-300 max-w-2xl mx-auto mb-8 text-lg"
            initial={{ opacity: 0, y: -20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            viewport={{ once: true }}
          >
            {getTranslation('instagramSubtitle', 'Lihat momen-momen perjalanan, tips, dan pembaruan terbaru dari layanan kami.')}
          </motion.p>
          <motion.div 
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <a
              href={getTranslation('socialInstagramUrl', 'https://www.instagram.com/umrohserviceco/')}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center justify-center px-10 py-4 border border-transparent text-lg font-semibold rounded-lg text-background bg-[#FFD700] hover:bg-yellow-400 transition-all duration-300 ease-in-out transform hover:scale-105 shadow-lg hover:shadow-yellow-500/50"
            >
              {getTranslation('instagramButton', 'Kunjungi Instagram Kami')}
              <Instagram className="ml-3 h-6 w-6" />
            </a>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default InstagramFeed;
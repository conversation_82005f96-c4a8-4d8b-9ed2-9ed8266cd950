import React, { useState, useContext } from 'react';
import { motion } from 'framer-motion';
import { Star, ChevronLeft, ChevronRight, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button.jsx';
import { Link } from 'react-router-dom';
import { useTestimonials } from '@/contexts/TestimonialsContext.jsx';
import { LanguageContext } from '@/contexts/LanguageContext.jsx';

const websiteIconLogo = "/favicon.svg";

const Testimonials = () => {
  const { testimonials, isLoading } = useTestimonials();
  const { translations } = useContext(LanguageContext);
  const [currentIndex, setCurrentIndex] = useState(0);

  const getTranslation = (key, fallback) => translations[key] || fallback;

  const handlePrev = () => {
    setCurrentIndex((prevIndex) => (prevIndex === 0 ? testimonials.length - 1 : prevIndex - 1));
  };

  const handleNext = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % testimonials.length);
  };

  if (isLoading) {
    return (
      <section className="py-20 bg-secondary text-white">
        <div className="container mx-auto px-4 md:px-6 text-center">
          <div className="h-64 w-full bg-gray-700/50 animate-pulse rounded-lg"></div>
        </div>
      </section>
    );
  }
  
  if (!testimonials || testimonials.length === 0) {
    return (
      <section className="py-20 bg-secondary text-white">
        <div className="container mx-auto px-4 md:px-6 text-center">
           <h2 className="text-4xl md:text-5xl font-extrabold text-white mb-4 tracking-tight">
             {getTranslation('testimonialsTitle', 'Apa Kata Mitra Kami')}
          </h2>
          <p className="text-gray-400">{getTranslation('noTestimonialsAvailable', 'Saat ini belum ada testimoni yang tersedia.')}</p>
        </div>
      </section>
    );
  }

  const currentTestimonial = testimonials[currentIndex];
  
  if (!currentTestimonial) return null;

  return (
    <section className="py-20 bg-secondary text-white overflow-hidden">
      <div className="container mx-auto px-4 md:px-6">
        <motion.div
          initial={{ opacity: 0, y: -30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.5 }}
          transition={{ duration: 0.7, type: "spring", stiffness: 100 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-extrabold text-white mb-4 tracking-tight">
             {getTranslation('testimonialsTitle', 'Apa Kata Mitra Kami')}
          </h2>
          <div className="w-24 h-1.5 bg-gradient-to-r from-[#FFD700] to-[#FFA500] mx-auto rounded-full"></div>
        </motion.div>

        <div className="relative max-w-3xl mx-auto">
          <motion.div
            key={currentIndex}
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -50 }}
            transition={{ duration: 0.5, type: "spring", stiffness: 120 }}
            className="bg-gradient-to-br from-gray-800 to-gray-900 p-8 md:p-10 rounded-xl shadow-2xl border border-gray-700/60 min-h-[320px]"
          >
            <div className="flex flex-col md:flex-row items-center mb-6 text-center md:text-left">
              <img
                src={currentTestimonial.avatar || websiteIconLogo} 
                alt={`Foto ${currentTestimonial.name}`}
                className="w-20 h-20 md:w-24 md:h-24 object-cover border-4 border-primary/50 bg-gray-700 rounded-full mb-4 md:mb-0 md:mr-6 flex-shrink-0"
              />
              <div className="flex-grow">
                <h3 className="text-xl md:text-2xl font-semibold text-white">{currentTestimonial.name}</h3>
                <p className="text-sm text-gray-400">{currentTestimonial.role}</p>
                <div className="flex mt-1.5 justify-center md:justify-start">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className={`w-5 h-5 ${i < currentTestimonial.stars ? 'text-amber-400 fill-amber-400' : 'text-gray-600'}`} />
                  ))}
                </div>
              </div>
            </div>
            <blockquote className="text-lg md:text-xl italic text-gray-300 leading-relaxed text-center">
              "{currentTestimonial.quote}"
            </blockquote>
          </motion.div>

          {testimonials.length > 1 && (
            <>
            <Button
              variant="outline"
              size="icon"
              className="absolute left-0 md:-left-16 top-1/2 -translate-y-1/2 transform bg-gray-700/50 hover:bg-primary hover:text-black text-white rounded-full shadow-md z-10"
              onClick={handlePrev}
              aria-label={getTranslation('prevTestimonial', "Testimoni sebelumnya")}
            >
              <ChevronLeft className="h-6 w-6" />
            </Button>
            <Button
              variant="outline"
              size="icon"
              className="absolute right-0 md:-right-16 top-1/2 -translate-y-1/2 transform bg-gray-700/50 hover:bg-primary hover:text-black text-white rounded-full shadow-md z-10"
              onClick={handleNext}
              aria-label={getTranslation('nextTestimonial', "Testimoni berikutnya")}
            >
              <ChevronRight className="h-6 w-6" />
            </Button>
            </>
          )}
        </div>
        
        <motion.div 
          className="text-center mt-12"
          initial={{ opacity: 0, y:20 }}
          whileInView={{ opacity: 1, y:0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay:0.3 }}
        >
          <Button asChild variant="link" className="text-primary hover:text-amber-300 text-lg font-medium group">
            <Link to="/testimonials">{getTranslation('testimonialViewAll', 'Lihat Semua Testimoni')} <ArrowRight className="ml-2 h-5 w-5 transition-transform duration-300 group-hover:translate-x-1"/></Link>
          </Button>
        </motion.div>

      </div>
    </section>
  );
};

export default Testimonials;
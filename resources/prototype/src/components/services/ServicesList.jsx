import React, { useState, useContext } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import ServiceDetailModal from '@/components/services/ServiceDetailModal';
import { LanguageContext } from '@/contexts/LanguageContext';
import { ArrowRight, Briefcase, Plane, Layers, FileText, BedDouble, Bus, Utensils, Map, ShoppingCart } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const serviceItemsList = [
  { 
    id: 'umrah-b2b', 
    icon: <Briefcase className="w-8 h-8 text-amber-300" />, 
    titleKey: 'service1Title', 
    descriptionKey: 'service1Desc', 
    defaultImageAlt: 'Mitra travel membahas paket handling umrah B2B', 
    imageNamePlaceholder: 'Tim sedang berdiskusi mengenai paket Umrah B2B', 
    detailTitleKey: '<PERSON><PERSON><PERSON> Handling B2B Umrah & <PERSON><PERSON>',
    serviceTypeForOrder: 'Full Handling Service (Land Arrangement)',
    imageUrl: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/a3809670215c8e8e446c4a86889e8302.jpg'
  },
  { 
    id: 'hajj-no-queue', 
    icon: <Plane className="w-8 h-8 text-amber-300" />, 
    titleKey: 'service2Title', 
    descriptionKey: 'service2Desc', 
    defaultImageAlt: 'Jamaah haji di depan Ka\'bah untuk paket haji tanpa antri', 
    imageNamePlaceholder: 'Pemandangan Ka\'bah untuk paket Haji tanpa antri', 
    detailTitleKey: 'Haji Tanpa Antri (Haji Furoda)',
    serviceTypeForOrder: 'PIF Service Request',
    imageUrl: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/a3809670215c8e8e446c4a86889e8302.jpg'
  },
  { 
    id: 'handling', 
    icon: <Layers className="w-8 h-8 text-amber-300" />, 
    titleKey: 'service3Title', 
    descriptionKey: 'service3Desc', 
    defaultImageAlt: 'Layanan handling koper jamaah umrah di bandara Saudi', 
    imageNamePlaceholder: 'Petugas handling membantu jamaah dengan barang bawaan di bandara', 
    detailTitleKey: 'Handling Bandara & Hotel',
    serviceTypeForOrder: 'Ground Handling',
    imageUrl: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/a3809670215c8e8e446c4a86889e8302.jpg'
  },
  { 
    id: 'visa', 
    icon: <FileText className="w-8 h-8 text-amber-300" />, 
    titleKey: 'service4Title', 
    descriptionKey: 'service4Desc', 
    defaultImageAlt: 'Pengurusan visa umrah dan haji resmi dan cepat', 
    imageNamePlaceholder: 'Dokumen visa dan paspor untuk pengurusan visa Umrah dan Haji', 
    detailTitleKey: 'Pengurusan Visa Umrah & Haji',
    serviceTypeForOrder: 'Apply Visa Umrah',
    imageUrl: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/a3809670215c8e8e446c4a86889e8302.jpg'
  },
  { 
    id: 'accommodation', 
    icon: <BedDouble className="w-8 h-8 text-amber-300" />, 
    titleKey: 'service5Title', 
    descriptionKey: 'service5Desc', 
    defaultImageAlt: 'Reservasi hotel bintang 5 dekat Masjidil Haram untuk jamaah umrah', 
    imageNamePlaceholder: 'Kamar hotel mewah di Mekkah untuk reservasi akomodasi', 
    detailTitleKey: 'Reservasi Akomodasi',
    serviceTypeForOrder: 'Booking Hotel Umrah',
    imageUrl: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/a3809670215c8e8e446c4a86889e8302.jpg'
  },
  { 
    id: 'transportation', 
    icon: <Bus className="w-8 h-8 text-amber-300" />, 
    titleKey: 'service6Title', 
    descriptionKey: 'service6Desc', 
    defaultImageAlt: 'Armada bus VIP untuk transportasi jamaah haji dan umrah di Saudi', 
    imageNamePlaceholder: 'Bus VIP modern untuk transportasi jamaah', 
    detailTitleKey: 'Transportasi Jamaah',
    serviceTypeForOrder: 'Ground Handling', // Closest match
    imageUrl: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/a3809670215c8e8e446c4a86889e8302.jpg'
  },
  { 
    id: 'catering', 
    icon: <Utensils className="w-8 h-8 text-amber-300" />, 
    titleKey: 'service7Title', 
    descriptionKey: 'service7Desc', 
    defaultImageAlt: 'Katering makanan halal Indonesia untuk jamaah umrah dan haji', 
    imageNamePlaceholder: 'Sajian katering makanan halal khas Indonesia untuk jamaah', 
    detailTitleKey: 'Katering Jamaah',
    serviceTypeForOrder: 'Ground Handling', // Closest match
    imageUrl: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/a3809670215c8e8e446c4a86889e8302.jpg'
  },
  { 
    id: 'saudi-tour', 
    icon: <Map className="w-8 h-8 text-amber-300" />, 
    titleKey: 'service8Title', 
    descriptionKey: 'service8Desc', 
    defaultImageAlt: 'Paket tour ziarah ke tempat bersejarah di Arab Saudi', 
    imageNamePlaceholder: 'Situs bersejarah di Al Ula untuk paket tour Arab Saudi', 
    detailTitleKey: 'Paket Tour & Ziarah Arab Saudi',
    serviceTypeForOrder: 'Ground Handling', // Closest match
    imageUrl: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/a3809670215c8e8e446c4a86889e8302.jpg'
  },
];

const ServicesList = () => {
  const [selectedService, setSelectedService] = useState(null);
  const { translations } = useContext(LanguageContext);
  const navigate = useNavigate();

  const openModal = (service) => setSelectedService(service);
  const closeModal = () => setSelectedService(null);
  
  const handleOrder = (service) => {
    const orderPackage = {
      id: service.id,
      name: service.title,
      type: 'Service',
      serviceType: service.serviceTypeForOrder
    };
    navigate('/order', { state: { orderPackage } });
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: { staggerChildren: 0.08 }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50, scale: 0.95 },
    visible: { 
      opacity: 1, 
      y: 0, 
      scale: 1,
      transition: { type: 'spring', stiffness: 100, damping: 12 }
    }
  };

  return (
    <section className="py-20 bg-gradient-to-b from-background to-secondary text-white">
      <div className="container mx-auto px-4 md:px-6">
        <motion.div
          initial={{ opacity: 0, y: -30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.7, type: "spring", stiffness: 100 }}
          className="text-center mb-16"
        >
          <h1 className="text-4xl sm:text-5xl md:text-6xl font-extrabold mb-6 tracking-tight gradient-text">
            {translations.servicesPageTitle || "Layanan Handling Umrah Haji Komprehensif Kami"}
          </h1>
          <p className="text-lg md:text-xl text-gray-400 max-w-3xl mx-auto">
            {translations.servicesPageSubtitle || "Solusi lengkap untuk kebutuhan handling umrah dan haji Anda, dari visa, akomodasi, hingga transportasi."}
          </p>
        </motion.div>

        <motion.div 
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
        >
          {serviceItemsList.map((service, index) => {
            const serviceTitle = translations[service.titleKey] || service.titleKey;
            const serviceDescription = translations[service.descriptionKey] || service.descriptionKey;
            const detailTitle = service.detailTitleKey;
            const imageAltText = translations[`${service.id}ImageAlt`] || service.defaultImageAlt || serviceTitle;

            return (
            <motion.div
              key={service.id}
              id={service.id}
              className="group bg-gradient-to-br from-gray-800 via-gray-900 to-black rounded-xl shadow-2xl overflow-hidden flex flex-col border border-gray-700/70 hover:border-amber-400/70 transition-all duration-300 ease-in-out transform hover:-translate-y-2 hover:shadow-glow"
              variants={itemVariants}
            >
              <div className="relative h-56 overflow-hidden">
                <img   
                  alt={imageAltText} 
                  className="w-full h-full object-cover transition-transform duration-500 ease-in-out group-hover:scale-110"
                  src={service.imageUrl}
                  loading={index > 3 ? "lazy" : "eager"}
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-black/10"></div>
                <div className="absolute top-4 right-4 p-3 bg-black/50 rounded-lg border border-gray-700">
                  {service.icon}
                </div>
              </div>
              
              <div className="p-6 flex flex-col flex-grow">
                <h2 className="text-xl font-bold text-white mb-3 group-hover:text-amber-400 transition-colors duration-300">
                  {serviceTitle}
                </h2>
                <p className="text-gray-400 text-sm mb-6 leading-relaxed flex-grow">
                  {serviceDescription}
                </p>
                <div className="mt-auto space-y-3">
                  <Button
                    onClick={() => openModal({ ...service, title: detailTitle, imageUrl: service.imageUrl, imageAlt: imageAltText })}
                    variant="outline"
                    className="w-full border-amber-400 text-amber-400 hover:bg-amber-400 hover:text-black font-semibold transition-all duration-300 ease-in-out group-hover:shadow-md group-hover:shadow-amber-500/30"
                  >
                    {translations.learnMore || "Pelajari Lebih Lanjut"} <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                  <Button
                    onClick={() => handleOrder({ ...service, title: serviceTitle })}
                    className="w-full gold-gradient text-black font-bold transition-all duration-300"
                  >
                    <ShoppingCart className="mr-2 h-4 w-4" />
                    {translations.orderService || "Pesan Layanan Ini"}
                  </Button>
                </div>
              </div>
            </motion.div>
            );
          })}
        </motion.div>
      </div>
      {selectedService && (
        <ServiceDetailModal
          isOpen={!!selectedService}
          onClose={closeModal}
          service={{...selectedService, title: selectedService.detailTitleKey}}
        />
      )}
    </section>
  );
};

export default ServicesList;
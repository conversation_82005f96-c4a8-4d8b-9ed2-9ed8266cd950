
import React, { useRef } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { AlertCircle, Upload, FileText, X, CheckCircle } from 'lucide-react';

const DocumentUploadItem = ({ label, file, onFileChange, onFileRemove, docKey }) => {
    const fileInputRef = useRef(null);

    const handleFileSelect = () => {
        fileInputRef.current.click();
    };

    const handleFileChange = (event) => {
        const selectedFile = event.target.files[0];
        if (selectedFile) {
            onFileChange(docKey, selectedFile);
        }
    };

    return (
        <div className="flex items-center justify-between py-3 border-b border-gray-700 last:border-b-0">
            <span className="text-gray-300">{label}</span>
            <Input
                type="file"
                ref={fileInputRef}
                className="hidden"
                onChange={handleFileChange}
                accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png"
            />
            {file ? (
                <div className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    <span className="text-sm text-gray-400 truncate max-w-[150px]">{file.name}</span>
                    <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => onFileRemove(docKey)}>
                        <X className="h-4 w-4 text-red-500" />
                    </Button>
                </div>
            ) : (
                <Button variant="outline" size="sm" onClick={handleFileSelect}>
                    <Upload className="mr-2 h-4 w-4" />
                    Upload
                </Button>
            )}
        </div>
    );
};

const DocumentUploadSection = ({ data, handleInputChange }) => {
    const handleFileChange = (docKey, file) => {
        handleInputChange('documents', docKey, file);
    };

    const handleFileRemove = (docKey) => {
        handleInputChange('documents', docKey, null);
    };

    const documentTypes = [
        { key: 'roomlist', label: 'Roomlist Jamaah (Excel/PDF)' },
        { key: 'ticket', label: 'Tiket Pesawat' },
        { key: 'visa', label: 'Visa Umrah' },
        { key: 'manifest', label: 'Manifest (Excel/PDF)' },
    ];

    return (
        <Card className="bg-gray-800/50 border-gray-700">
            <CardHeader>
                <CardTitle className="flex items-center text-xl font-bold text-white">
                    <AlertCircle className="h-6 w-6 mr-3 text-amber-400" />
                    Upload Dokumen (Opsional)
                </CardTitle>
                <CardDescription>
                    Unggah dokumen untuk mempercepat proses verifikasi.
                </CardDescription>
            </CardHeader>
            <CardContent>
                <div className="space-y-2">
                    {documentTypes.map(({ key, label }) => (
                        <DocumentUploadItem
                            key={key}
                            docKey={key}
                            label={label}
                            file={data[key]}
                            onFileChange={handleFileChange}
                            onFileRemove={handleFileRemove}
                        />
                    ))}
                </div>
            </CardContent>
        </Card>
    );
};

export default DocumentUploadSection;

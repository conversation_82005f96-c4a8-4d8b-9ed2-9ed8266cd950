import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { MessageSquare } from 'lucide-react';

const NotesSection = ({ data, handleInputChange }) => {
    return (
        <Card className="bg-gray-800/50 border-gray-700">
            <CardHeader>
                <CardTitle className="flex items-center">
                    <span className="bg-amber-400 text-black rounded-full h-8 w-8 flex items-center justify-center font-bold text-lg mr-4">10</span>
                    Catatan Tambahan
                </CardTitle>
                <CardDescription>
                    Tuliskan permintaan atau catatan khusus lainnya untuk pesanan ini.
                </CardDescription>
            </CardHeader>
            <CardContent>
                <div className="relative">
                    <MessageSquare className="absolute top-3 left-3 h-5 w-5 text-gray-400" />
                    <Textarea 
                        placeholder="Contoh: <PERSON>hon sediakan 1 kamar dengan akses kursi roda, atau permintaan khusus lainnya..." 
                        value={data} 
                        onChange={e => handleInputChange(e.target.value)}
                        className="pl-10"
                        rows={4}
                    />
                </div>
            </CardContent>
        </Card>
    );
};

export default NotesSection;
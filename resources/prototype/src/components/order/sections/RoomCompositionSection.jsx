import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle, CardDescription } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Info, Users } from 'lucide-react';

const RoomCompositionSection = ({ data, handleInputChange, totalPax }) => {
    return (
        <Card className="bg-gray-800/50 border-gray-700">
            <CardHeader>
                <CardTitle className="flex items-center">
                    <span className="bg-amber-400 text-black rounded-full h-8 w-8 flex items-center justify-center font-bold text-lg mr-4">3</span>
                    Komposisi Kamar & Jumlah Jamaah
                </CardTitle>
                <CardDescription>Masukkan jumlah kamar untuk menghitung total jamaah secara otomatis.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="space-y-2">
                        <Label htmlFor="quad">Quad (4 pax)</Label>
                        <Input id="quad" type="number" min="0" placeholder="0" value={data.quad} onChange={(e) => handleInputChange('quad', e.target.value)} />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="triple">Triple (3 pax)</Label>
                        <Input id="triple" type="number" min="0" placeholder="0" value={data.triple} onChange={(e) => handleInputChange('triple', e.target.value)} />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="double">Double (2 pax)</Label>
                        <Input id="double" type="number" min="0" placeholder="0" value={data.double} onChange={(e) => handleInputChange('double', e.target.value)} />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="single">Single (1 pax)</Label>
                        <Input id="single" type="number" min="0" placeholder="0" value={data.single} onChange={(e) => handleInputChange('single', e.target.value)} />
                    </div>
                </div>

                <div className="flex items-center justify-center p-4 bg-gray-900/50 rounded-lg">
                    <Users className="h-8 w-8 text-amber-400 mr-4" />
                    <div>
                        <p className="text-sm text-gray-400">Total Jamaah</p>
                        <p className="text-3xl font-bold text-white">{totalPax || 0}</p>
                    </div>
                </div>

                <Alert>
                    <Info className="h-4 w-4" />
                    <AlertDescription>
                        1 grup maksimal 47 pax per bus. Jika jumlah melebihi 47, sistem akan membaginya ke grup/bus selanjutnya secara otomatis.
                    </AlertDescription>
                </Alert>
            </CardContent>
        </Card>
    );
};

export default RoomCompositionSection;
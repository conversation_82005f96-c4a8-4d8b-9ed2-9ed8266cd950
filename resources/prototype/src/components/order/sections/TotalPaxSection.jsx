import React from 'react';
import { Card, Card<PERSON>ontent, Card<PERSON>eader, CardTitle, CardDescription } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Info } from 'lucide-react';

const TotalPaxSection = ({ totalPax, handleInputChange }) => {
    return (
        <Card className="bg-gray-800/50 border-gray-700">
            <CardHeader>
                <CardTitle className="flex items-center">
                    <span className="bg-amber-400 text-black rounded-full h-8 w-8 flex items-center justify-center font-bold text-lg mr-4">3</span>
                    Jumlah Total Jamaah
                </CardTitle>
                <CardDescription>Masukkan jumlah keseluruhan jamaah untuk perhitungan otomatis.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
                <div className="space-y-2">
                    <Label htmlFor="totalPax">Total Jamaah</Label>
                    <Input 
                        id="totalPax" 
                        type="number" 
                        min="1" 
                        placeholder="Contoh: 45" 
                        value={totalPax} 
                        onChange={(e) => handleInputChange('totalPax', e.target.value)} 
                    />
                </div>
                <Alert>
                    <Info className="h-4 w-4" />
                    <AlertDescription>
                        1 grup maksimal 47 pax per bus. Jika jumlah melebihi 47, sistem akan membaginya ke grup/bus selanjutnya secara otomatis.
                    </AlertDescription>
                </Alert>
            </CardContent>
        </Card>
    );
};

export default TotalPaxSection;
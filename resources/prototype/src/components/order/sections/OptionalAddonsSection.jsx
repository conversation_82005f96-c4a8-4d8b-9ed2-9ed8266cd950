import React, { useMemo } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle, CardDescription } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Info, UserCheck, UtensilsCrossed, Mountain, Train } from 'lucide-react';
import { pricingStructure } from '@/data/pricingStructure';

const OptionalAddonsSection = ({ data, handleInputChange, setFormData, packageType }) => {
    
    const isHandlingAirportOnly = packageType === 'handling_airport_only';
    const isLaB2b = packageType === 'la_b2b';
    
    const handleCheckboxChange = (section, key, value) => {
        setFormData(prev => ({
            ...prev,
            optionalAddons: {
                ...prev.optionalAddons,
                [section]: {
                    ...prev.optionalAddons[section],
                    [key]: value
                }
            }
        }));
    };
    
    const handleMutawwifNeededChange = (checked) => {
        setFormData(prev => ({
            ...prev,
            mutawwifRequest: {
                ...prev.mutawwifRequest,
                needed: checked
            }
        }));
    };

    const thaifTourData = data.optionalAddons?.thaif_tour || {};
    const haramainTrainData = data.optionalAddons?.haramain_train || {};
    const culinaryData = data.optionalAddons?.culinary || {};
    const mutawwifRequestData = data.mutawwifRequest || {};

    const totalHotelNights = useMemo(() => {
        if (isLaB2b) return 0; // Hotel details not applicable for LA B2B in this form
        return data.hotelDetails?.hotels?.reduce((acc, hotel) => acc + (parseInt(hotel.nights, 10) || 0), 0) || 0;
    }, [data.hotelDetails, isLaB2b]);

    const mutawwifDuration = totalHotelNights > 0 ? totalHotelNights + 1 : 0;

    const mutawwifCostPerPax = useMemo(() => {
        if (!mutawwifRequestData.needed || data.totalPax === 0 || mutawwifDuration === 0 || isLaB2b) {
            return 0;
        }
        const { cost_per_day_sr, sr_to_idr_rate, sr_to_usd_rate } = pricingStructure.optional_addons.mutawwif;
        const costPerPax = (cost_per_day_sr * sr_to_idr_rate * mutawwifDuration) / data.totalPax * sr_to_usd_rate;
        return costPerPax;
    }, [mutawwifRequestData.needed, data.totalPax, mutawwifDuration, isLaB2b]);

    const totalMutawwifCost = mutawwifCostPerPax * data.totalPax;

    const AccordionCheckboxItem = ({ id, checked, onCheckedChange, label, price }) => (
        <div className="flex items-center space-x-3 bg-gray-900/50 p-4 rounded-lg transition-all hover:bg-gray-900/70">
            <Checkbox id={id} checked={checked} onCheckedChange={onCheckedChange} />
            <Label htmlFor={id} className="flex-grow text-base text-white cursor-pointer">{label}</Label>
            <span className="text-amber-400 font-semibold">{price}</span>
        </div>
    );

    return (
        <Card className="bg-gray-800/50 border-gray-700">
            <CardHeader>
                <CardTitle className="flex items-center">
                    <span className="bg-amber-400 text-black rounded-full h-8 w-8 flex items-center justify-center font-bold text-lg mr-4">5</span>
                    Tambahan Opsional & Kustomisasi
                </CardTitle>
                <CardDescription>
                    Pilih layanan tambahan untuk melengkapi paket Anda. Harga akan disesuaikan secara otomatis.
                </CardDescription>
            </CardHeader>
            <CardContent>
                <Accordion type="multiple" defaultValue={['mutawwif_request']} className="w-full space-y-2">
                    
                    {!isHandlingAirportOnly && (
                        <AccordionItem value="mutawwif_request" className="bg-gray-900/30 rounded-lg border border-gray-700">
                            <AccordionTrigger className="text-lg font-semibold text-white px-6">
                                <div className="flex items-center gap-3">
                                    <UserCheck className="h-6 w-6 text-amber-400" />
                                    Permintaan Mutawwif
                                </div>
                            </AccordionTrigger>
                            <AccordionContent className="px-6 pt-4 space-y-6">
                                <div className="flex items-center space-x-3">
                                    <Checkbox id="mutawwif_needed" checked={mutawwifRequestData.needed} onCheckedChange={handleMutawwifNeededChange} />
                                    <Label htmlFor="mutawwif_needed" className="text-base text-white cursor-pointer">
                                        {isLaB2b ? "Saya ingin menentukan kriteria mutawwif" : "Saya ingin menambah mutawwif"}
                                    </Label>
                                </div>

                                {mutawwifRequestData.needed && (
                                    <div className="space-y-4 pl-6 border-l-2 border-amber-400">
                                        <Alert>
                                            <Info className="h-4 w-4" />
                                            <AlertDescription>
                                                {isLaB2b ? 
                                                    "Mutawwif sudah termasuk dalam paket LA B2B. Silakan tentukan kriteria yang Anda butuhkan tanpa biaya tambahan."
                                                    :
                                                    <>
                                                        Biaya mutawwif dihitung otomatis berdasarkan total durasi hotel
                                                        ({mutawwifDuration > 0 ? `${totalHotelNights} malam + 1 hari = ${mutawwifDuration} hari` : 'Isi tanggal & durasi hotel dahulu'}).
                                                        <br/>
                                                        {mutawwifDuration > 0 && data.totalPax > 0 && (
                                                            <>
                                                                Biaya per pax: <span className="font-bold text-amber-300">${mutawwifCostPerPax.toFixed(2)}</span>.
                                                                Total biaya: <span className="font-bold text-amber-300">${totalMutawwifCost.toFixed(2)}</span>.
                                                            </>
                                                        )}
                                                    </>
                                                }
                                            </AlertDescription>
                                        </Alert>
                                    </div>
                                )}
                            </AccordionContent>
                        </AccordionItem>
                    )}

                    {!isHandlingAirportOnly && (
                        <AccordionItem value="culinary" className="bg-gray-900/30 rounded-lg border border-gray-700">
                            <AccordionTrigger className="text-lg font-semibold text-white px-6">
                                <div className="flex items-center gap-3">
                                    <UtensilsCrossed className="h-6 w-6 text-amber-400" />
                                    Kuliner Khas Arab
                                </div>
                            </AccordionTrigger>
                            <AccordionContent className="px-6 pt-4 grid grid-cols-1 gap-4">
                                <AccordionCheckboxItem
                                    id="al_rumansiah"
                                    checked={!!culinaryData.al_rumansiah}
                                    onCheckedChange={(checked) => handleCheckboxChange('culinary', 'al_rumansiah', checked)}
                                    label="Makan di Restoran Al Rumansiah"
                                    price="$9/pax"
                                />
                            </AccordionContent>
                        </AccordionItem>
                    )}

                    {!isHandlingAirportOnly && (
                        <AccordionItem value="thaif_tour" className="bg-gray-900/30 rounded-lg border border-gray-700">
                            <AccordionTrigger className="text-lg font-semibold text-white px-6">
                                <div className="flex items-center gap-3">
                                    <Mountain className="h-6 w-6 text-amber-400" />
                                    Tour Thaif
                                </div>
                            </AccordionTrigger>
                            <AccordionContent className="px-6 pt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                                <AccordionCheckboxItem
                                    id="thaif_bus_charter"
                                    checked={!!thaifTourData.bus_charter}
                                    onCheckedChange={(checked) => handleCheckboxChange('thaif_tour', 'bus_charter', checked)}
                                    label="Bus Charter"
                                    price="$550/grup"
                                />
                                <AccordionCheckboxItem
                                    id="thaif_cable_car"
                                    checked={!!thaifTourData.cable_car}
                                    onCheckedChange={(checked) => handleCheckboxChange('thaif_tour', 'cable_car', checked)}
                                    label="Cable Car"
                                    price="$19/pax"
                                />
                                <AccordionCheckboxItem
                                    id="thaif_tobogan"
                                    checked={!!thaifTourData.tobogan}
                                    onCheckedChange={(checked) => handleCheckboxChange('thaif_tour', 'tobogan', checked)}
                                    label="Tobogan"
                                    price="$10/pax"
                                />
                                <AccordionCheckboxItem
                                    id="thaif_lunch"
                                    checked={!!thaifTourData.lunch}
                                    onCheckedChange={(checked) => handleCheckboxChange('thaif_tour', 'lunch', checked)}
                                    label="Makan Siang"
                                    price="$6/pax"
                                />
                            </AccordionContent>
                        </AccordionItem>
                    )}

                    <AccordionItem value="haramain_train" className="bg-gray-900/30 rounded-lg border border-gray-700">
                        <AccordionTrigger className="text-lg font-semibold text-white px-6">
                            <div className="flex items-center gap-3">
                                <Train className="h-6 w-6 text-amber-400" />
                                Tiket Kereta Haramain
                            </div>
                        </AccordionTrigger>
                        <AccordionContent className="px-6 pt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                            <AccordionCheckboxItem
                                id="train_makkah_medina"
                                checked={!!haramainTrainData.makkah_madinah}
                                onCheckedChange={(checked) => handleCheckboxChange('haramain_train', 'makkah_madinah', checked)}
                                label="Makkah ➝ Madinah"
                                price="$65/pax"
                            />
                            <AccordionCheckboxItem
                                id="train_medina_makkah"
                                checked={!!haramainTrainData.madinah_makkah}
                                onCheckedChange={(checked) => handleCheckboxChange('haramain_train', 'madinah_makkah', checked)}
                                label="Madinah ➝ Makkah"
                                price="$65/pax"
                            />
                            <AccordionCheckboxItem
                                id="train_medina_jed"
                                checked={!!haramainTrainData.madinah_jed}
                                onCheckedChange={(checked) => handleCheckboxChange('haramain_train', 'madinah_jed', checked)}
                                label="Madinah ➝ JED Airport"
                                price="$55/pax"
                            />
                            <AccordionCheckboxItem
                                id="train_jed_medina"
                                checked={!!haramainTrainData.jed_madinah}
                                onCheckedChange={(checked) => handleCheckboxChange('haramain_train', 'jed_madinah', checked)}
                                label="JED Airport ➝ Madinah"
                                price="$55/pax"
                            />
                        </AccordionContent>
                    </AccordionItem>
                    
                </Accordion>
            </CardContent>
        </Card>
    );
};

export default OptionalAddonsSection;
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Package, Briefcase, Link, Building, Wrench } from 'lucide-react';

const PackageTypeSection = ({ value, onValueChange }) => {
    const navigate = useNavigate();

    const packageTypes = [
        { value: 'la_b2b', label: 'Paket Land Arrangement (LA) B2B', description: 'Paket siap pakai termasuk hotel, makan, dan handling.', icon: <Package className="h-5 w-5 mr-3" /> },
        { value: 'handling_service', label: 'Paket Handling Service Saja', description: 'Layanan operasional di lapangan tanpa akomodasi.', icon: <Briefcase className="h-5 w-5 mr-3" /> },
        { value: 'bundling_visa_handling', label: 'Paket Bundling Visa + Handling', description: 'Layanan operasional digabung dengan pengurusan visa.', icon: <Link className="h-5 w-5 mr-3" /> },
        { value: 'handling_airport_only', label: 'Paket Handling Airport Only', description: 'Penjemputan dan layanan di bandara JED/MED.', icon: <Building className="h-5 w-5 mr-3" /> },
        { value: 'custom_request', label: 'Buat Paket Custom Sendiri', description: 'Rancang paket dari nol sesuai kebutuhan spesifik Anda.', icon: <Wrench className="h-5 w-5 mr-3" /> },
    ];

    const handleChange = (newValue) => {
        if (newValue === 'custom_request') {
            navigate('/order/custom-request');
        } else {
            onValueChange(newValue);
        }
    };

    return (
        <Card className="bg-gray-800/50 border-gray-700">
            <CardHeader>
                <CardTitle className="flex items-center">
                    <span className="bg-amber-400 text-black rounded-full h-8 w-8 flex items-center justify-center font-bold text-lg mr-4">2</span>
                    Pilih Jenis Layanan
                </CardTitle>
                <CardDescription>Formulir akan menyesuaikan berdasarkan pilihan Anda.</CardDescription>
            </CardHeader>
            <CardContent>
                <RadioGroup value={value} onValueChange={handleChange} className="space-y-3">
                    {packageTypes.map(pkg => (
                        <Label key={pkg.value} htmlFor={pkg.value} className="flex items-start p-4 border rounded-lg cursor-pointer transition-all duration-300 has-[:checked]:border-amber-400 has-[:checked]:bg-amber-500/10 has-[:checked]:shadow-md border-gray-700 bg-gray-900/50 hover:border-amber-400/50">
                            <RadioGroupItem value={pkg.value} id={pkg.value} className="mt-1" />
                            <div className="ml-4">
                                <p className="font-semibold text-white flex items-center">{pkg.icon}{pkg.label}</p>
                                <p className="text-sm text-gray-400">{pkg.description}</p>
                            </div>
                        </Label>
                    ))}
                </RadioGroup>
            </CardContent>
        </Card>
    );
};

export default PackageTypeSection;
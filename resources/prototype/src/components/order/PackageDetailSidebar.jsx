import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import { Package, MapPin, Star, Clock, CheckCircle2, XCircle } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { AnimatePresence, motion } from 'framer-motion';

const DetailItem = ({ icon, label, value }) => (
    <div className="flex items-start">
        {icon}
        <div>
            <p className="text-sm text-gray-400">{label}</p>
            <p className="font-semibold text-white">{value}</p>
        </div>
    </div>
);

const PackageDetailSidebar = ({ pkg, translations }) => {
    const getTranslation = (key, fallback) => translations[key] || fallback;

    return (
        <AnimatePresence>
            {pkg && (
                <motion.div
                    initial={{ opacity: 0, y: 10, height: 0 }}
                    animate={{ opacity: 1, y: 0, height: 'auto' }}
                    exit={{ opacity: 0, y: -10, height: 0 }}
                    transition={{ duration: 0.3 }}
                >
                    <Card className="bg-gray-900/80 border-gray-700">
                        <CardHeader>
                            <CardTitle>Detail Paket</CardTitle>
                            <CardDescription>Rincian dari paket yang Anda pilih.</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-6">
                                <DetailItem 
                                    icon={<Package className="h-5 w-5 mr-3 text-amber-400 mt-0.5 flex-shrink-0" />} 
                                    label="Nama Paket"
                                    value={getTranslation(pkg.nameKey, pkg.name)}
                                />
                                <DetailItem 
                                    icon={<Clock className="h-5 w-5 mr-3 text-amber-400 mt-0.5 flex-shrink-0" />} 
                                    label="Durasi"
                                    value={getTranslation(pkg.durationKey, pkg.duration)}
                                />
                                <DetailItem 
                                    icon={<Star className="h-5 w-5 mr-3 text-amber-400 mt-0.5 flex-shrink-0" />} 
                                    label="Bintang"
                                    value={`${pkg.stars} Bintang`}
                                />
                                <DetailItem 
                                    icon={<MapPin className="h-5 w-5 mr-3 text-amber-400 mt-0.5 flex-shrink-0" />} 
                                    label="Hotel Madinah"
                                    value={pkg.hotelMadinah}
                                />
                                <DetailItem 
                                    icon={<MapPin className="h-5 w-5 mr-3 text-amber-400 mt-0.5 flex-shrink-0" />} 
                                    label="Hotel Makkah"
                                    value={pkg.hotelMakkah}
                                />
                                
                                <div>
                                    <h4 className="font-semibold text-green-400 mb-3 flex items-center text-base"><CheckCircle2 size={18} className="mr-2"/> {getTranslation('includes', 'Termasuk')}</h4>
                                    <ul className="space-y-2 text-sm text-gray-300">
                                        {(pkg.includes || []).map((item, i) => (
                                            <li key={i} className="flex items-start">
                                                <CheckCircle2 className="w-4 h-4 mr-2.5 mt-0.5 flex-shrink-0 text-green-400/80" />
                                                <span>{getTranslation(item.key, item.default)}</span>
                                            </li>
                                        ))}
                                    </ul>
                                </div>
                                
                                <div>
                                    <h4 className="font-semibold text-red-400 mb-3 flex items-center text-base"><XCircle size={18} className="mr-2"/> {getTranslation('excludes', 'Tidak Termasuk')}</h4>
                                    <ul className="space-y-2 text-sm text-gray-300">
                                        {(pkg.excludes || []).map((item, i) => (
                                            <li key={i} className="flex items-start">
                                                <XCircle className="w-4 h-4 mr-2.5 mt-0.5 flex-shrink-0 text-red-400/80" />
                                                <span>{getTranslation(item.key, item.default)}</span>
                                            </li>
                                        ))}
                                    </ul>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </motion.div>
            )}
        </AnimatePresence>
    );
};

export default PackageDetailSidebar;
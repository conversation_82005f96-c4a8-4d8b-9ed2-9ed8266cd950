import React from 'react';
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";

const PifServiceFormSection = ({ pifServices, setFormData }) => {
    const pifOptions = ["Handling Basic", "Handling Premium", "Handling VIP", "Visa Only", "Visa + Bus", "Bus Only", "Hotel", "Hotel + Check-in", "Mutawwif", "Train (Haramain)", "Airport Handling (Indonesia)", "Airport Handling (Saudi)"];

    const handlePifServiceChange = (service, checked) => {
        setFormData(prev => {
            const newPifServices = checked
                ? [...prev.pifServices, service]
                : prev.pifServices.filter(s => s !== service);
            return { ...prev, pifServices: newPifServices };
        });
    };

    return (
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {pifOptions.map(opt => (
                <div key={opt} className="flex items-center space-x-2">
                    <Checkbox 
                        id={`pif_${opt}`} 
                        checked={pifServices.includes(opt)} 
                        onCheckedChange={(c) => handlePifServiceChange(opt, c)} 
                    />
                    <Label htmlFor={`pif_${opt}`}>{opt}</Label>
                </div>
            ))}
        </div>
    );
};

export default PifServiceFormSection;
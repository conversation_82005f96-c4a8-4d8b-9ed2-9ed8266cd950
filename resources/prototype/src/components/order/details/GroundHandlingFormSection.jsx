import React from 'react';
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

const GroundHandlingFormSection = ({ data, onInputChange }) => {
    const groundHandlingDetails = {
        Basic: "Welcome drink (Zamzam + Kurma), handling hotel & airport, koper, snack 4x, dokumentasi, tips porter & driver",
        Premium: "Semua fasilitas Basic + X-Banner, Al Baik 1x, snack premium, parcel buah, air mineral",
        VIP: "Semua fasilitas Basic + Arabian kuliner Al Romanshiah, snack VIP, parcel VIP, <PERSON><PERSON><PERSON> galon, Al Bai<PERSON> (Madinah-Makkah)"
    };

    return (
        <div className="space-y-4">
             <RadioGroup defaultValue="Basic" value={data.type} onValueChange={(v) => onInputChange('type', v)}>
                <div className="flex items-center space-x-2"><RadioGroupItem value="Basic" id="gh_basic" /><Label htmlFor="gh_basic">Basic</Label></div>
                <div className="flex items-center space-x-2"><RadioGroupItem value="Premium" id="gh_premium" /><Label htmlFor="gh_premium">Premium</Label></div>
                <div className="flex items-center space-x-2"><RadioGroupItem value="VIP" id="gh_vip" /><Label htmlFor="gh_vip">VIP</Label></div>
            </RadioGroup>
            <Card className="bg-gray-900/50 border-gray-700 mt-4">
                <CardHeader><CardTitle className="text-lg text-amber-400">Fasilitas Termasuk</CardTitle></CardHeader>
                <CardContent><p>{groundHandlingDetails[data.type]}</p></CardContent>
            </Card>
        </div>
    );
};

export default GroundHandlingFormSection;
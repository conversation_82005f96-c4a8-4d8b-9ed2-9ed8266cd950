import React from 'react';
import { v4 as uuidv4 } from 'uuid';
import { format } from "date-fns";
import { id } from "date-fns/locale";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Calendar as CalendarIcon, PlusCircle, Trash2 } from 'lucide-react';
import { calculateNights } from '@/lib/dateUtils';
import { Badge } from '@/components/ui/badge';

const HotelFormSection = ({ hotelsData, setFormData }) => {
    const addHotel = () => {
        setFormData(prev => ({
            ...prev,
            hotels: [...prev.hotels, { 
                id: uuidv4(), city: '', hotelName: '', checkIn: null, checkOut: null, 
                rooms: { double: '', triple: '', quad: '', quint: '' }, totalPax: '' 
            }]
        }));
    };
    
    const removeHotel = (id) => {
        setFormData(prev => ({
            ...prev,
            hotels: prev.hotels.filter(hotel => hotel.id !== id)
        }));
    };
    
    const handleHotelChange = (id, field, value) => {
        setFormData(prev => ({
            ...prev,
            hotels: prev.hotels.map(hotel => hotel.id === id ? { ...hotel, [field]: value } : hotel)
        }));
    };
    
    const handleHotelRoomChange = (id, roomType, value) => {
        setFormData(prev => ({
            ...prev,
            hotels: prev.hotels.map(hotel => 
                hotel.id === id 
                ? { ...hotel, rooms: { ...(hotel.rooms || {}), [roomType]: value } } 
                : hotel
            )
        }));
    };

    return (
        <div className="space-y-4">
            {hotelsData.map((hotel, index) => {
                const nights = calculateNights(hotel.checkIn, hotel.checkOut);
                const hotelRooms = hotel.rooms || {}; // Ensure hotel.rooms is not undefined
                return (
                    <Card key={hotel.id} className="bg-gray-900/50 border-gray-700">
                        <CardHeader className="flex flex-row items-center justify-between pb-2">
                            <CardTitle className="text-lg text-amber-400">Hotel #{index + 1}</CardTitle>
                            <Button variant="ghost" size="icon" onClick={() => removeHotel(hotel.id)}>
                                <Trash2 className="h-4 w-4 text-red-500"/>
                            </Button>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label>Kota</Label>
                                    <Select value={hotel.city} onValueChange={(value) => handleHotelChange(hotel.id, 'city', value)}>
                                        <SelectTrigger><SelectValue placeholder="Pilih kota" /></SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="Makkah">Makkah</SelectItem>
                                            <SelectItem value="Madinah">Madinah</SelectItem>
                                            <SelectItem value="Jeddah">Jeddah</SelectItem>
                                            <SelectItem value="Thaif">Thaif</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                                <div className="space-y-2">
                                    <Label>Nama Hotel</Label>
                                    <Input placeholder="Contoh: Swissotel Al Maqam" value={hotel.hotelName} onChange={e => handleHotelChange(hotel.id, 'hotelName', e.target.value)} />
                                </div>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 items-end">
                                <div className="space-y-2">
                                    <Label>Tanggal Check-in</Label>
                                    <Popover>
                                        <PopoverTrigger asChild>
                                            <Button variant="outline" className="w-full justify-start text-left font-normal">
                                                <CalendarIcon className="mr-2 h-4 w-4" />
                                                {hotel.checkIn ? format(hotel.checkIn, "PPP", { locale: id }) : <span>Pilih tanggal</span>}
                                            </Button>
                                        </PopoverTrigger>
                                        <PopoverContent className="w-auto p-0"><Calendar mode="single" selected={hotel.checkIn} onSelect={(date) => handleHotelChange(hotel.id, 'checkIn', date)}/></PopoverContent>
                                    </Popover>
                                </div>
                                <div className="space-y-2">
                                    <Label>Tanggal Check-out</Label>
                                    <Popover>
                                        <PopoverTrigger asChild>
                                            <Button variant="outline" className="w-full justify-start text-left font-normal">
                                                <CalendarIcon className="mr-2 h-4 w-4" />
                                                {hotel.checkOut ? format(hotel.checkOut, "PPP", { locale: id }) : <span>Pilih tanggal</span>}
                                            </Button>
                                        </PopoverTrigger>
                                        <PopoverContent className="w-auto p-0"><Calendar mode="single" selected={hotel.checkOut} onSelect={(date) => handleHotelChange(hotel.id, 'checkOut', date)}/></PopoverContent>
                                    </Popover>
                                </div>
                                {nights > 0 && <Badge variant="secondary" className="w-fit">{nights} Malam</Badge>}
                            </div>
                            <div className="space-y-2">
                                <Label>Jumlah Kamar</Label>
                                <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                                    <Input type="number" placeholder="Double" value={hotelRooms.double || ''} onChange={e => handleHotelRoomChange(hotel.id, 'double', e.target.value)} />
                                    <Input type="number" placeholder="Triple" value={hotelRooms.triple || ''} onChange={e => handleHotelRoomChange(hotel.id, 'triple', e.target.value)} />
                                    <Input type="number" placeholder="Quad" value={hotelRooms.quad || ''} onChange={e => handleHotelRoomChange(hotel.id, 'quad', e.target.value)} />
                                    <Input type="number" placeholder="Quint" value={hotelRooms.quint || ''} onChange={e => handleHotelRoomChange(hotel.id, 'quint', e.target.value)} />
                                </div>
                            </div>
                             <div className="space-y-2">
                                <Label>Total Pax</Label>
                                <Input type="number" placeholder="Jumlah total tamu" value={hotel.totalPax} onChange={e => handleHotelChange(hotel.id, 'totalPax', e.target.value)} />
                            </div>
                        </CardContent>
                    </Card>
                )
            })}
            <Button variant="outline" onClick={addHotel} className="w-full">
                <PlusCircle className="mr-2 h-4 w-4"/> Tambah Hotel
            </Button>
        </div>
    );
};

export default HotelFormSection;
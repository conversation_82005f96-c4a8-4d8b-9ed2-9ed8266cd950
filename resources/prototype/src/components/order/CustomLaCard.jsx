import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Wrench } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const CustomLaCard = () => {
    const navigate = useNavigate();

    return (
        <div className="w-full max-w-sm">
            <Button 
                size="lg" 
                variant="outline"
                className="w-full border-amber-400/50 text-amber-400 hover:bg-amber-400 hover:text-black font-bold text-lg py-6 transition-all duration-300 ease-in-out group hover:shadow-lg hover:shadow-amber-500/20"
                onClick={() => navigate('/custom-la-order')}
            >
                <Wrench className="mr-3 h-6 w-6 transition-transform group-hover:rotate-12" />
                Buat Custom Paket LA
            </Button>
        </div>
    );
};

export default CustomLaCard;
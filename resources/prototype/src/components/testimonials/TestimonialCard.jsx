import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card.jsx';
import { Star } from 'lucide-react';

const TestimonialCard = ({ testimonial, index }) => {
  const cardVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        delay: index * 0.1,
      },
    },
  };

  return (
    <motion.div variants={cardVariants} initial="hidden" animate="visible" whileHover={{ y: -5 }}>
      <Card className="h-full flex flex-col bg-gradient-to-br from-gray-900 to-black border-gray-700 shadow-lg hover:shadow-amber-500/20 transition-shadow duration-300">
        <CardContent className="p-6 flex-grow flex flex-col">
          <div className="flex items-center mb-4">
            <img 
              className="w-16 h-16 rounded-full mr-4 border-2 border-amber-400 object-cover bg-gray-700"
              alt={`Avatar of ${testimonial.name}`}
              src={testimonial.avatar || `https://ui-avatars.com/api/?name=${testimonial.name}&background=0D1117&color=fff`} />
            <div className="flex-grow">
              <p className="font-bold text-lg text-white">{testimonial.name}</p>
              <p className="text-sm text-gray-400">{testimonial.role}</p>
            </div>
          </div>
          <div className="flex text-amber-400 mb-4">
            {[...Array(testimonial.stars || 0)].map((_, i) => (
              <Star key={i} className="w-5 h-5 fill-current" />
            ))}
            {[...Array(5 - (testimonial.stars || 0))].map((_, i) => (
              <Star key={i + (testimonial.stars || 0)} className="w-5 h-5 text-gray-600" />
            ))}
          </div>
          <blockquote className="text-gray-300 italic border-l-4 border-amber-500 pl-4 flex-grow">
            "{testimonial.quote}"
          </blockquote>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default TestimonialCard;
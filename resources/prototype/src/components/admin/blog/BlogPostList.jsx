import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card.jsx';
import { Button } from '@/components/ui/button.jsx';
import { Badge } from '@/components/ui/badge.jsx';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog.jsx';
import { Edit, Trash2, Eye, Calendar, User } from 'lucide-react';

const BlogPostList = ({ posts, isLoading, onDeletePost, onPreviewPost }) => {
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadge = (status) => {
    const variants = {
      'Published': 'bg-green-500/20 text-green-400 border-green-500/30',
      'Draft': 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30',
      'Archived': 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    };
    return variants[status] || variants['Draft'];
  };

  return (
    <Card className="bg-gray-800/50 border-gray-700/50">
      <CardHeader>
        <CardTitle className="text-white">Daftar Artikel</CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="text-center py-8">
            <p className="text-gray-400">Memuat artikel...</p>
          </div>
        ) : posts.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-400">Tidak ada artikel yang ditemukan.</p>
          </div>
        ) : (
          <div className="space-y-4">
            {posts.map((post) => (
              <motion.div
                key={post.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="flex items-center justify-between p-4 bg-gray-700/30 rounded-lg border border-gray-600/30 hover:bg-gray-700/50 transition-colors"
              >
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="font-semibold text-white truncate">{post.title}</h3>
                    <Badge className={getStatusBadge(post.status)}>
                      {post.status}
                    </Badge>
                    {post.category && (
                      <Badge variant="outline" className="text-primary border-primary">
                        {post.category}
                      </Badge>
                    )}
                  </div>
                  <div className="flex items-center gap-4 text-sm text-gray-400">
                    <span className="flex items-center gap-1">
                      <User size={14} />
                      {post.author || 'Admin'}
                    </span>
                    <span className="flex items-center gap-1">
                      <Calendar size={14} />
                      {formatDate(post.createdAt)}
                    </span>
                    {post.views !== undefined && (
                      <span className="flex items-center gap-1">
                        <Eye size={14} />
                        {post.views} views
                      </span>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-2 ml-4">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => onPreviewPost(post)}
                    className="text-blue-400 border-blue-400 hover:bg-blue-400/10"
                  >
                    <Eye size={16} />
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    asChild
                    className="text-primary border-primary hover:bg-primary/10"
                  >
                    <Link to={`/admin/post-editor/${post.id}`}>
                      <Edit size={16} />
                    </Link>
                  </Button>
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button
                        size="sm"
                        variant="outline"
                        className="text-red-400 border-red-400 hover:bg-red-400/10"
                      >
                        <Trash2 size={16} />
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent className="bg-gray-800 border-gray-700">
                      <AlertDialogHeader>
                        <AlertDialogTitle className="text-white">Hapus Artikel</AlertDialogTitle>
                        <AlertDialogDescription className="text-gray-400">
                          Apakah Anda yakin ingin menghapus artikel "{post.title}"? Tindakan ini tidak dapat dibatalkan.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel className="bg-gray-700 text-white border-gray-600 hover:bg-gray-600">
                          Batal
                        </AlertDialogCancel>
                        <AlertDialogAction
                          onClick={() => onDeletePost(post.id)}
                          className="bg-red-600 text-white hover:bg-red-700"
                        >
                          Hapus
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default BlogPostList;
import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card.jsx';
import { Button } from '@/components/ui/button.jsx';
import { Label } from '@/components/ui/label.jsx';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select.jsx';

const BlogSettings = ({ settings, onSettingsChange, onSave, onCancel }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-gray-800/50 border border-gray-700/50 rounded-lg p-6"
    >
      <h3 className="text-xl font-semibold text-white mb-4">Pengaturan Blog</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div>
          <Label htmlFor="postsPerPage" className="text-white">Artikel per Halaman</Label>
          <Select 
            value={settings.postsPerPage.toString()} 
            onValueChange={(value) => onSettingsChange({ postsPerPage: parseInt(value) })}
          >
            <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-gray-800 text-white border-gray-700">
              <SelectItem value="3">3 artikel</SelectItem>
              <SelectItem value="6">6 artikel</SelectItem>
              <SelectItem value="9">9 artikel</SelectItem>
              <SelectItem value="12">12 artikel</SelectItem>
              <SelectItem value="15">15 artikel</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-3">
          <Label className="text-white">Tampilan Artikel</Label>
          <div className="space-y-2">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={settings.showExcerpt}
                onChange={(e) => onSettingsChange({ showExcerpt: e.target.checked })}
                className="rounded border-gray-600 bg-gray-700"
              />
              <span className="text-white text-sm">Tampilkan Excerpt</span>
            </label>
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={settings.showAuthor}
                onChange={(e) => onSettingsChange({ showAuthor: e.target.checked })}
                className="rounded border-gray-600 bg-gray-700"
              />
              <span className="text-white text-sm">Tampilkan Penulis</span>
            </label>
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={settings.showDate}
                onChange={(e) => onSettingsChange({ showDate: e.target.checked })}
                className="rounded border-gray-600 bg-gray-700"
              />
              <span className="text-white text-sm">Tampilkan Tanggal</span>
            </label>
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={settings.showCategory}
                onChange={(e) => onSettingsChange({ showCategory: e.target.checked })}
                className="rounded border-gray-600 bg-gray-700"
              />
              <span className="text-white text-sm">Tampilkan Kategori</span>
            </label>
          </div>
        </div>
        <div className="space-y-3">
          <Label className="text-white">Fitur Blog</Label>
          <div className="space-y-2">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={settings.enableSearch}
                onChange={(e) => onSettingsChange({ enableSearch: e.target.checked })}
                className="rounded border-gray-600 bg-gray-700"
              />
              <span className="text-white text-sm">Aktifkan Pencarian</span>
            </label>
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={settings.enableCategoryFilter}
                onChange={(e) => onSettingsChange({ enableCategoryFilter: e.target.checked })}
                className="rounded border-gray-600 bg-gray-700"
              />
              <span className="text-white text-sm">Filter Kategori</span>
            </label>
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={settings.enableComments}
                onChange={(e) => onSettingsChange({ enableComments: e.target.checked })}
                className="rounded border-gray-600 bg-gray-700"
              />
              <span className="text-white text-sm">Aktifkan Komentar</span>
            </label>
          </div>
        </div>
      </div>
      <div className="flex justify-end gap-3 mt-6">
        <Button
          variant="outline"
          onClick={onCancel}
          className="text-gray-400 border-gray-600 hover:bg-gray-700"
        >
          Batal
        </Button>
        <Button
          onClick={onSave}
          className="bg-primary text-black hover:bg-primary/90"
        >
          Simpan Pengaturan
        </Button>
      </div>
    </motion.div>
  );
};

export default BlogSettings;
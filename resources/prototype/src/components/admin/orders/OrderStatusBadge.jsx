import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Clock, CheckCircle, XCircle, Loader } from 'lucide-react';

const statusConfig = {
    Baru: {
        label: 'Baru',
        color: 'bg-blue-500/20 text-blue-400 border-blue-500/30',
        icon: <Clock size={14} className="mr-1.5" />
    },
    Diproses: {
        label: 'Diproses',
        color: 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30',
        icon: <Loader size={14} className="mr-1.5 animate-spin" />
    },
    Selesai: {
        label: 'Seles<PERSON>',
        color: 'bg-green-500/20 text-green-400 border-green-500/30',
        icon: <CheckCircle size={14} className="mr-1.5" />
    },
    Dibatalkan: {
        label: 'Dibatalkan',
        color: 'bg-red-500/20 text-red-400 border-red-500/30',
        icon: <XCircle size={14} className="mr-1.5" />
    },
};

const OrderStatusBadge = ({ status }) => {
    const config = statusConfig[status] || { label: status, color: 'bg-gray-500/20 text-gray-400', icon: null };

    return (
        <Badge variant="outline" className={`text-sm font-medium flex items-center w-fit ${config.color}`}>
            {config.icon}
            {config.label}
        </Badge>
    );
};

export default OrderStatusBadge;
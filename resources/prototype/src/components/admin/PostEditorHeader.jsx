import React from 'react';
import { Button } from '@/components/ui/button.jsx';
import { ArrowLeft, Eye, Save, Clock, FileText } from 'lucide-react';

const PostEditorHeader = ({
  isNewPost,
  postData,
  isSaving,
  onBack,
  onPreview,
  onSubmit
}) => {
  return (
    <div className="flex items-center justify-between mb-6">
      <div className="flex items-center gap-4">
        <Button 
          type="button" 
          variant="outline" 
          onClick={onBack} 
          className="text-gray-300 border-gray-600 hover:bg-gray-700 hover:text-white"
        >
          <ArrowLeft size={18} className="mr-2" /> Kembali
        </Button>
        <div className="flex items-center gap-2 text-gray-400">
          <FileText size={16} />
          <span className="text-sm">
            {isNewPost ? 'Artikel Baru' : 'Edit Artikel'}
          </span>
        </div>
      </div>
      <div className="flex items-center gap-2">
        {postData.updatedAt && (
          <span className="text-xs text-gray-400 flex items-center gap-1.5">
            <Clock size={14} />
            Update terakhir: {new Date(postData.updatedAt).toLocaleString('id-ID')}
          </span>
        )}
        <Button 
          type="button" 
          variant="outline" 
          onClick={onPreview} 
          className="text-white border-gray-600 hover:bg-gray-700 hover:text-white"
        >
          <Eye size={16} className="mr-2" /> Preview
        </Button>
        <Button 
          type="submit" 
          disabled={isSaving}
          onClick={onSubmit}
          className="gold-gradient text-black font-semibold hover:opacity-90 disabled:opacity-50"
        >
          <Save size={18} className="mr-2" /> 
          {isSaving ? 'Menyimpan...' : (isNewPost ? 'Simpan Post' : 'Update Post')}
        </Button>
      </div>
    </div>
  );
};

export default PostEditorHeader;
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card.jsx';
import { Button } from '@/components/ui/button.jsx';
import { Input } from '@/components/ui/input.jsx';
import { Label } from '@/components/ui/label.jsx';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select.jsx';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table.jsx';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter as DialogPrimitiveFooter } from "@/components/ui/dialog.jsx";
import { PlusCircle, Trash2, Upload, Download, Save, Search, Edit2 } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast.js';
import * as XLSX from 'xlsx';

const ContactManagementTab = () => {
  const { toast } = useToast();
  const [contacts, setContacts] = useState([]);
  const [isContactModalOpen, setIsContactModalOpen] = useState(false);
  const [currentContact, setCurrentContact] = useState(null);
  const [contactName, setContactName] = useState('');
  const [contactEmail, setContactEmail] = useState('');
  const [contactWa, setContactWa] = useState('');
  const [contactCountry, setContactCountry] = useState('');
  const [contactCategory, setContactCategory] = useState('');
  const [contactSearchTerm, setContactSearchTerm] = useState('');
  const [contactFilterCountry, setContactFilterCountry] = useState('all');
  const [contactFilterCategory, setContactFilterCategory] = useState('all');

  const initialContacts = [
    { id: '1', name: 'Ahmad Subagyo', email: '<EMAIL>', wa: '6281234567890', country: 'Indonesia', category: 'Agen', dateAdded: '2025-06-10' },
    { id: '2', name: 'John Doe', email: '<EMAIL>', wa: '11234567890', country: 'USA', category: 'Potensial', dateAdded: '2025-06-12' },
  ];

  useEffect(() => {
    const storedContacts = localStorage.getItem('marketingContacts');
    setContacts(storedContacts ? JSON.parse(storedContacts) : initialContacts);
  }, []);

  const filteredContacts = contacts.filter(contact => 
    (contact.name.toLowerCase().includes(contactSearchTerm.toLowerCase()) || contact.email.toLowerCase().includes(contactSearchTerm.toLowerCase()) || contact.wa.includes(contactSearchTerm)) &&
    (contactFilterCountry === 'all' || contact.country === contactFilterCountry) &&
    (contactFilterCategory === 'all' || contact.category === contactFilterCategory)
  );

  const handleOpenContactModal = (contact = null) => {
    setCurrentContact(contact);
    if (contact) {
      setContactName(contact.name);
      setContactEmail(contact.email);
      setContactWa(contact.wa);
      setContactCountry(contact.country);
      setContactCategory(contact.category);
    } else {
      setContactName(''); setContactEmail(''); setContactWa(''); setContactCountry(''); setContactCategory('');
    }
    setIsContactModalOpen(true);
  };

  const handleSaveContact = () => {
    if (!contactName || (!contactEmail && !contactWa)) {
      toast({ title: "Data Tidak Lengkap", description: "Nama dan salah satu (Email/WA) wajib diisi.", variant: "destructive" });
      return;
    }
    const newContact = { 
      id: currentContact ? currentContact.id : Date.now().toString(), 
      name: contactName, email: contactEmail, wa: contactWa, country: contactCountry, category: contactCategory,
      dateAdded: currentContact ? currentContact.dateAdded : new Date().toISOString().split('T')[0]
    };
    const updatedContacts = currentContact ? contacts.map(c => c.id === currentContact.id ? newContact : c) : [...contacts, newContact];
    setContacts(updatedContacts);
    localStorage.setItem('marketingContacts', JSON.stringify(updatedContacts));
    toast({ title: `Kontak ${currentContact ? 'Diperbarui' : 'Ditambahkan'}`, description: `${contactName} berhasil disimpan.` });
    setIsContactModalOpen(false);
  };

  const handleDeleteContact = (contactId) => {
    const updatedContacts = contacts.filter(c => c.id !== contactId);
    setContacts(updatedContacts);
    localStorage.setItem('marketingContacts', JSON.stringify(updatedContacts));
    toast({ title: "Kontak Dihapus" });
  };

  const handleImportContacts = (event) => {
    const file = event.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const data = new Uint8Array(e.target.result);
                const workbook = XLSX.read(data, { type: 'array' });
                const sheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[sheetName];
                const json = XLSX.utils.sheet_to_json(worksheet);
                
                if (json.length === 0) {
                  toast({ title: "File Kosong", description: "File CSV/Excel tidak mengandung data.", variant: "destructive" });
                  return;
                }

                const firstRow = json[0];
                if (!firstRow.Nama || (!firstRow.Email && !firstRow['No WhatsApp'])) {
                   toast({ title: "Format Kolom Salah", description: "Pastikan file memiliki kolom 'Nama', dan salah satu dari 'Email' atau 'No WhatsApp'.", variant: "destructive" });
                   return;
                }

                const newContacts = json.map((row, index) => ({
                    id: Date.now().toString() + index,
                    name: row.Nama || '',
                    email: row.Email || '',
                    wa: String(row['No WhatsApp'] || ''),
                    country: row.Negara || '',
                    category: row.Kategori || '',
                    dateAdded: new Date().toISOString().split('T')[0]
                }));

                const updatedContacts = [...contacts, ...newContacts];
                setContacts(updatedContacts);
                localStorage.setItem('marketingContacts', JSON.stringify(updatedContacts));
                toast({ title: "Kontak Diimpor", description: `${newContacts.length} kontak berhasil diimpor.` });
            } catch (error) {
                console.error("Error importing contacts:", error);
                toast({ title: "Gagal Impor", description: "Terjadi kesalahan saat memproses file. Pastikan formatnya benar.", variant: "destructive" });
            }
        };
        reader.readAsArrayBuffer(file);
        event.target.value = null;
    }
  };
  
  const handleExportContacts = () => {
    if (filteredContacts.length === 0) {
      toast({ title: "Tidak Ada Data", description: "Tidak ada kontak untuk diekspor.", variant: "destructive" });
      return;
    }
    const worksheet = XLSX.utils.json_to_sheet(filteredContacts.map(c => ({
      Nama: c.name, Email: c.email, 'No WhatsApp': c.wa, Negara: c.country, Kategori: c.category, 'Tgl Ditambahkan': c.dateAdded
    })));
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Kontak");
    XLSX.writeFile(workbook, "kontak_marketing.xlsx");
    toast({ title: "Kontak Diekspor", description: "Data kontak berhasil diekspor ke file Excel." });
  };

  const uniqueCountries = ['all', ...new Set(contacts.map(c => c.country).filter(Boolean))];
  const uniqueCategories = ['all', ...new Set(contacts.map(c => c.category).filter(Boolean))];

  return (
    <Card className="bg-gray-800 border-gray-700">
      <CardHeader>
        <CardTitle className="text-xl text-primary">Daftar Kontak Target</CardTitle>
        <div className="flex flex-col sm:flex-row gap-2 mt-2">
          <Button onClick={() => handleOpenContactModal()}><PlusCircle size={18} className="mr-2" />Tambah Kontak Manual</Button>
          <Input type="file" accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" onChange={handleImportContacts} className="bg-gray-700 border-gray-600 file:text-primary file:mr-2 file:font-semibold" />
          <Button onClick={handleExportContacts} variant="outline" className="text-gray-300 border-gray-600 hover:bg-gray-700"><Download size={18} className="mr-2" />Export Kontak</Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <Input placeholder="Cari nama, email, WA..." value={contactSearchTerm} onChange={e => setContactSearchTerm(e.target.value)} className="bg-gray-700 border-gray-600" />
          <Select value={contactFilterCountry} onValueChange={setContactFilterCountry}>
            <SelectTrigger className="bg-gray-700 border-gray-600"><SelectValue placeholder="Filter Negara" /></SelectTrigger>
            <SelectContent className="bg-gray-800 text-white border-gray-700">{uniqueCountries.map(c => <SelectItem key={c} value={c} className="hover:bg-primary/20">{c === 'all' ? 'Semua Negara' : c}</SelectItem>)}</SelectContent>
          </Select>
          <Select value={contactFilterCategory} onValueChange={setContactFilterCategory}>
            <SelectTrigger className="bg-gray-700 border-gray-600"><SelectValue placeholder="Filter Kategori" /></SelectTrigger>
            <SelectContent className="bg-gray-800 text-white border-gray-700">{uniqueCategories.map(c => <SelectItem key={c} value={c} className="hover:bg-primary/20">{c === 'all' ? 'Semua Kategori' : c}</SelectItem>)}</SelectContent>
          </Select>
        </div>
        <div className="overflow-x-auto max-h-[50vh]">
          <Table>
            <TableHeader><TableRow className="hover:bg-gray-700/30"><TableHead>Nama</TableHead><TableHead>Email</TableHead><TableHead>No. WA</TableHead><TableHead>Negara</TableHead><TableHead>Kategori</TableHead><TableHead>Aksi</TableHead></TableRow></TableHeader>
            <TableBody>
              {filteredContacts.map(contact => (
                <TableRow key={contact.id} className="hover:bg-gray-700/30">
                  <TableCell>{contact.name}</TableCell><TableCell>{contact.email}</TableCell><TableCell>{contact.wa}</TableCell><TableCell>{contact.country}</TableCell><TableCell>{contact.category}</TableCell>
                  <TableCell>
                    <Button variant="ghost" size="icon" onClick={() => handleOpenContactModal(contact)} className="hover:text-primary"><Edit2 size={16} /></Button>
                    <Button variant="ghost" size="icon" onClick={() => handleDeleteContact(contact.id)} className="hover:text-red-500"><Trash2 size={16} /></Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
           {filteredContacts.length === 0 && <p className="text-center text-gray-400 py-4">Tidak ada kontak ditemukan.</p>}
        </div>
      </CardContent>
      <Dialog open={isContactModalOpen} onOpenChange={setIsContactModalOpen}>
        <DialogContent className="bg-gray-800 border-gray-700 text-white">
          <DialogHeader><DialogTitle className="text-primary">{currentContact ? 'Edit Kontak' : 'Tambah Kontak Baru'}</DialogTitle></DialogHeader>
          <div className="space-y-3 py-2">
            <div><Label htmlFor="contactNameModal">Nama</Label><Input id="contactNameModal" value={contactName} onChange={e => setContactName(e.target.value)} className="bg-gray-700 border-gray-600" /></div>
            <div><Label htmlFor="contactEmailModal">Email</Label><Input id="contactEmailModal" type="email" value={contactEmail} onChange={e => setContactEmail(e.target.value)} className="bg-gray-700 border-gray-600" /></div>
            <div><Label htmlFor="contactWaModal">No. WhatsApp (dengan kode negara)</Label><Input id="contactWaModal" value={contactWa} onChange={e => setContactWa(e.target.value)} className="bg-gray-700 border-gray-600" /></div>
            <div><Label htmlFor="contactCountryModal">Negara</Label><Input id="contactCountryModal" value={contactCountry} onChange={e => setContactCountry(e.target.value)} className="bg-gray-700 border-gray-600" /></div>
            <div><Label htmlFor="contactCategoryModal">Kategori</Label><Input id="contactCategoryModal" value={contactCategory} onChange={e => setContactCategory(e.target.value)} className="bg-gray-700 border-gray-600" /></div>
          </div>
          <DialogPrimitiveFooter>
            <Button variant="outline" onClick={() => setIsContactModalOpen(false)} className="text-gray-300 border-gray-600">Batal</Button>
            <Button onClick={handleSaveContact} className="gold-gradient text-black"><Save size={16} className="mr-2"/> Simpan Kontak</Button>
          </DialogPrimitiveFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
};

export default ContactManagementTab;
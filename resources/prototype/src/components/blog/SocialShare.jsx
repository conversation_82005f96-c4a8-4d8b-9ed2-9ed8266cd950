import React, { useContext } from 'react';
import {
  TwitterShareButton,
  FacebookShareButton,
  WhatsappShareButton,
  LinkedinShareButton,
  TwitterIcon,
  FacebookIcon,
  WhatsappIcon,
  LinkedinIcon,
} from 'react-share';
import { Link2, Share2 } from 'lucide-react';
import { Button } from '@/components/ui/button.jsx';
import { useToast } from '@/components/ui/use-toast.js';
import { LanguageContext } from '@/contexts/LanguageContext.jsx';

const SocialShare = ({ title, url }) => {
  const { toast } = useToast();
  const { translations } = useContext(LanguageContext);

  const copyToClipboard = () => {
    navigator.clipboard.writeText(url).then(() => {
      toast({
        title: translations.linkCopiedTitle || "Tautan Disalin!",
        description: translations.linkCopiedDesc || "Tautan artikel telah berhasil disalin ke clipboard.",
        className: "bg-green-600 text-white border-green-700",
      });
    }, (err) => {
      console.error('Could not copy text: ', err);
      toast({
        variant: "destructive",
        title: translations.copyErrorTitle || "Gagal Menyalin",
        description: translations.copyErrorDesc || "Tidak dapat menyalin tautan. Silakan coba lagi.",
      });
    });
  };

  return (
    <div className="mt-12 pt-8 border-t border-gray-700/50">
      <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
        <Share2 size={20} className="mr-3 text-primary" />
        {translations.shareArticle || 'Bagikan Artikel Ini'}
      </h3>
      <div className="flex flex-wrap items-center gap-3">
        <TwitterShareButton url={url} title={title}>
          <TwitterIcon size={40} round />
        </TwitterShareButton>
        <FacebookShareButton url={url} quote={title}>
          <FacebookIcon size={40} round />
        </FacebookShareButton>
        <WhatsappShareButton url={url} title={title} separator=":: ">
          <WhatsappIcon size={40} round />
        </WhatsappShareButton>
        <LinkedinShareButton url={url} title={title}>
          <LinkedinIcon size={40} round />
        </LinkedinShareButton>
        <Button
          variant="outline"
          size="icon"
          onClick={copyToClipboard}
          aria-label="Copy link"
          className="w-10 h-10 bg-gray-700 hover:bg-primary hover:text-black border-gray-600 hover:border-primary rounded-full transition-colors duration-300"
        >
          <Link2 size={20} />
        </Button>
      </div>
    </div>
  );
};

export default SocialShare;
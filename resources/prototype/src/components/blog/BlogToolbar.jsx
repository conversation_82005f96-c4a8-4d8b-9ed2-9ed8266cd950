import React from 'react';
import { motion } from 'framer-motion';
import { Input } from '@/components/ui/input.jsx';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select.jsx';
import { Button } from '@/components/ui/button.jsx';
import { Search, Filter, X, LayoutGrid, List } from 'lucide-react';

const BlogToolbar = ({
  searchTerm,
  onSearchChange,
  filterCategory,
  onCategoryChange,
  categories,
  translations,
  onClearFilters,
  hasActiveFilters,
  viewMode,
  onViewModeChange,
  postsPerPage,
  onPostsPerPageChange
}) => {
  const postsPerPageOptions = [6, 12, 18, 24];

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="mb-12"
    >
      <div className="p-4 md:p-6 bg-gray-800/50 rounded-xl shadow-lg border border-gray-700/50 backdrop-blur-sm">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 items-center">
          {/* Search Input */}
          <div className="relative">
            <Input
              type="text"
              placeholder={translations.searchBlogPlaceholder || "Cari artikel..."}
              value={searchTerm}
              onChange={(e) => onSearchChange(e.target.value)}
              className="bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:border-primary pl-10"
            />
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
          </div>

          {/* Category Filter */}
          <Select value={filterCategory} onValueChange={onCategoryChange}>
            <SelectTrigger className="bg-gray-700 border-gray-600 text-white focus:border-primary">
              <SelectValue placeholder={translations.filterCategoryPlaceholder || "Filter Kategori"} />
            </SelectTrigger>
            <SelectContent className="bg-gray-800 text-white border-gray-700">
              {categories.map(cat => (
                <SelectItem
                  key={cat}
                  value={cat}
                  className="hover:bg-primary/20 focus:bg-primary/20 cursor-pointer"
                >
                  {cat === 'all' ? (translations.allCategories || 'Semua Kategori') : cat}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Clear Filters Button */}
          {hasActiveFilters && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClearFilters}
              className="text-gray-400 hover:bg-gray-700 hover:text-white md:col-start-3 md:justify-self-end"
            >
              <X size={16} className="mr-1" />
              {translations.clearFilters || "Hapus Filter"}
            </Button>
          )}
        </div>

        <div className="border-t border-gray-700/50 my-4"></div>

        <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-400">{translations.view || "Tampilan"}:</span>
            <Button
              variant={viewMode === 'grid' ? 'default' : 'outline'}
              size="sm"
              onClick={() => onViewModeChange('grid')}
              className="view-toggle-btn"
              data-active={viewMode === 'grid'}
            >
              <LayoutGrid size={16} />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'outline'}
              size="sm"
              onClick={() => onViewModeChange('list')}
              className="view-toggle-btn"
              data-active={viewMode === 'list'}
            >
              <List size={16} />
            </Button>
          </div>
          
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-400">{translations.show || "Tampil"}:</span>
            <Select value={String(postsPerPage)} onValueChange={(val) => onPostsPerPageChange(Number(val))}>
              <SelectTrigger className="w-[80px] bg-gray-700 border-gray-600 text-white focus:border-primary">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-gray-800 text-white border-gray-700">
                {postsPerPageOptions.map(option => (
                  <SelectItem key={option} value={String(option)}>{option}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            <span className="text-sm text-gray-400">{translations.perPage || "per halaman"}</span>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default BlogToolbar;
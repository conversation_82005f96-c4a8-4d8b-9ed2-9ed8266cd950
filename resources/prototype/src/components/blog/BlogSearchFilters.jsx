import React from 'react';
import { motion } from 'framer-motion';
import { Input } from '@/components/ui/input.jsx';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select.jsx';
import { Button } from '@/components/ui/button.jsx';
import { Search, Filter, X } from 'lucide-react';

const BlogSearchFilters = ({
  searchTerm,
  onSearchChange,
  filterCategory,
  onCategoryChange,
  categories,
  blogSettings,
  translations,
  onClearFilters,
  hasActiveFilters
}) => {
  if (!blogSettings.enableSearch && !blogSettings.enableCategoryFilter) {
    return null;
  }

  return (
    <motion.div 
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="mb-10 p-6 bg-gray-800/50 rounded-lg shadow-lg border border-gray-700/50"
    >
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <Filter size={20} className="text-primary" />
          <h3 className="text-lg font-semibold text-white">
            {translations.filterAndSearch || "Filter & Pencarian"}
          </h3>
        </div>
        
        {hasActiveFilters && (
          <Button
            variant="outline"
            size="sm"
            onClick={onClearFilters}
            className="text-gray-400 border-gray-600 hover:bg-gray-700 hover:text-white"
          >
            <X size={16} className="mr-1" />
            {translations.clearFilters || "Hapus Filter"}
          </Button>
        )}
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {blogSettings.enableSearch && (
          <div className="relative">
            <Input 
              type="text"
              placeholder={translations.searchBlogPlaceholder || "Cari artikel..."}
              value={searchTerm}
              onChange={(e) => onSearchChange(e.target.value)}
              className="bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:border-primary pl-10"
            />
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
          </div>
        )}
        
        {blogSettings.enableCategoryFilter && (
          <Select value={filterCategory} onValueChange={onCategoryChange}>
            <SelectTrigger className="bg-gray-700 border-gray-600 text-white focus:border-primary">
              <SelectValue placeholder={translations.filterCategoryPlaceholder || "Filter Kategori"} />
            </SelectTrigger>
            <SelectContent className="bg-gray-800 text-white border-gray-700">
              {categories.map(cat => (
                <SelectItem 
                  key={cat} 
                  value={cat} 
                  className="hover:bg-primary/20 focus:bg-primary/20 cursor-pointer"
                >
                  {cat === 'all' ? (translations.allCategories || 'Semua Kategori') : cat}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}
      </div>
    </motion.div>
  );
};

export default BlogSearchFilters;
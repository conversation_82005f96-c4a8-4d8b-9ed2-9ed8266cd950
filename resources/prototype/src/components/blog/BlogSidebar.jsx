import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card.jsx';
import { Button } from '@/components/ui/button.jsx';
import { ArrowRight, CalendarDays, Tag, TrendingUp } from 'lucide-react';
import { useBlog } from '@/contexts/BlogContext.jsx';

const BlogSidebar = ({ relatedPosts, categories, formatDate, currentPostId }) => {
  const { getLatestPosts } = useBlog();
  const placeholderImage = "https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/0ef586d5607ce8348b025632bfe2a445.jpg";
  
  const latestPosts = getLatestPosts(5, currentPostId);

  const sidebarVariants = {
    hidden: { opacity: 0, x: 20 },
    visible: { 
      opacity: 1, 
      x: 0,
      transition: { 
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <motion.div
      variants={sidebarVariants}
      initial="hidden"
      animate="visible"
      className="space-y-8 sticky top-8"
    >
      {/* Latest Posts */}
      {latestPosts.length > 0 && (
        <motion.div variants={itemVariants}>
          <Card className="bg-gradient-to-br from-gray-800 to-gray-900 border border-gray-700/50 text-white shadow-xl">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg font-semibold text-primary flex items-center">
                <TrendingUp size={20} className="mr-2" />
                Artikel Terbaru
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {latestPosts.map((post) => (
                <div key={post.id} className="group">
                  <Link 
                    to={`/blog/${post.slug}`}
                    className="block hover:bg-gray-700/30 p-3 rounded-lg transition-colors duration-200"
                  >
                    <div className="flex gap-3">
                      <div className="flex-shrink-0 w-16 h-16 rounded-md overflow-hidden">
                        <img
                          src={post.featured_image || placeholderImage}
                          alt={post.title}
                          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-white text-sm line-clamp-2 group-hover:text-primary transition-colors duration-200">
                          {post.title}
                        </h4>
                        <div className="flex items-center text-xs text-gray-400 mt-1">
                          <CalendarDays size={12} className="mr-1" />
                          {formatDate(post.publish_date)}
                        </div>
                        {post.category && (
                          <div className="flex items-center text-xs text-gray-500 mt-1">
                            <Tag size={12} className="mr-1" />
                            {post.category}
                          </div>
                        )}
                      </div>
                    </div>
                  </Link>
                </div>
              ))}
              <div className="pt-2 border-t border-gray-700/50">
                <Button asChild variant="link" className="text-primary hover:text-primary/80 p-0 h-auto font-medium">
                  <Link to="/blog">
                    Lihat Semua Artikel <ArrowRight size={14} className="ml-1" />
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Categories */}
      {categories.length > 0 && (
        <motion.div variants={itemVariants}>
          <Card className="bg-gradient-to-br from-gray-800 to-gray-900 border border-gray-700/50 text-white shadow-xl">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg font-semibold text-primary flex items-center">
                <Tag size={20} className="mr-2" />
                Kategori
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {categories.slice(0, 8).map((category) => (
                  <Link
                    key={category.name}
                    to={`/blog?category=${encodeURIComponent(category.name)}`}
                    className="flex items-center justify-between p-2 rounded-md hover:bg-gray-700/30 transition-colors duration-200 group"
                  >
                    <span className="text-gray-300 group-hover:text-primary transition-colors duration-200">
                      {category.name}
                    </span>
                    <span className="text-xs bg-gray-700 text-gray-400 px-2 py-1 rounded-full group-hover:bg-primary group-hover:text-black transition-colors duration-200">
                      {category.count}
                    </span>
                  </Link>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Related Posts */}
      {relatedPosts.length > 0 && (
        <motion.div variants={itemVariants}>
          <Card className="bg-gradient-to-br from-gray-800 to-gray-900 border border-gray-700/50 text-white shadow-xl">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg font-semibold text-primary">
                Artikel Terkait
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {relatedPosts.map((post) => (
                <div key={post.id} className="group">
                  <Link 
                    to={`/blog/${post.slug}`}
                    className="block hover:bg-gray-700/30 p-3 rounded-lg transition-colors duration-200"
                  >
                    <div className="flex gap-3">
                      <div className="flex-shrink-0 w-16 h-16 rounded-md overflow-hidden">
                        <img
                          src={post.featured_image || placeholderImage}
                          alt={post.title}
                          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-white text-sm line-clamp-2 group-hover:text-primary transition-colors duration-200">
                          {post.title}
                        </h4>
                        <div className="flex items-center text-xs text-gray-400 mt-1">
                          <CalendarDays size={12} className="mr-1" />
                          {formatDate(post.publish_date)}
                        </div>
                      </div>
                    </div>
                  </Link>
                </div>
              ))}
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Call to Action */}
      <motion.div variants={itemVariants}>
        <Card className="bg-gradient-to-br from-primary/20 to-primary/10 border border-primary/30 text-white shadow-xl">
          <CardContent className="p-6 text-center">
            <h3 className="text-lg font-semibold text-primary mb-3">
              Butuh Konsultasi Umrah?
            </h3>
            <p className="text-gray-300 text-sm mb-4">
              Tim ahli kami siap membantu merencanakan perjalanan umrah Anda dengan layanan handling profesional.
            </p>
            <Button asChild className="w-full bg-primary text-black hover:bg-primary/90 font-semibold">
              <Link to="/contact">
                Hubungi Kami Sekarang
              </Link>
            </Button>
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  );
};

export default BlogSidebar;
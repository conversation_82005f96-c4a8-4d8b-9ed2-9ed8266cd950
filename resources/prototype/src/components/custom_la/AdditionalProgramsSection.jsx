
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle, CardDescription } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Mountain, Train, Users } from 'lucide-react';

const CheckboxItem = ({ id, checked, onCheckedChange, label }) => (
    <div className="flex items-center space-x-3">
        <Checkbox id={id} checked={checked} onCheckedChange={onCheckedChange} />
        <Label htmlFor={id} className="text-gray-300 font-normal cursor-pointer">{label}</Label>
    </div>
);

const AdditionalProgramsSection = ({ formData, handleInputChange, handleCheckboxChange, handleThaifChange }) => {
    const thaifTour = formData?.thaifTour || {};
    const haramainTrain = formData?.haramainTrain || {};
    const routes = haramainTrain?.routes || {};

    return (
        <Card className="bg-gray-800/50 border-gray-700">
            <CardHeader>
                <CardTitle className="flex items-center">
                    <span className="bg-amber-400 text-black rounded-full h-8 w-8 flex items-center justify-center font-bold text-lg mr-4">F</span>
                    Program Tambahan (Opsional)
                </CardTitle>
                <CardDescription>Pilih program tambahan untuk memperkaya pengalaman perjalanan.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-8">
                {/* Tour Thaif */}
                <div className="space-y-4 p-4 border border-gray-700 rounded-lg">
                    <h4 className="font-semibold text-white text-lg flex items-center"><Mountain className="mr-2 h-5 w-5 text-amber-400" /> Tour Thaif</h4>
                    <CheckboxItem
                        id="thaif-tour"
                        checked={thaifTour.enabled}
                        onCheckedChange={(checked) => handleThaifChange('enabled', checked)}
                        label="Ingin program Tour Thaif"
                    />
                    {thaifTour.enabled && (
                        <div className="pl-8 space-y-3">
                            <CheckboxItem
                                id="thaif-bus"
                                checked={thaifTour.charterBus}
                                onCheckedChange={(checked) => handleThaifChange('charterBus', checked)}
                                label="Tambahan Bus Charter"
                            />
                            <CheckboxItem
                                id="thaif-cablecar"
                                checked={thaifTour.cableCarTicket}
                                onCheckedChange={(checked) => handleThaifChange('cableCarTicket', checked)}
                                label="Tiket Kereta Gantung (Cable Car)"
                            />
                            <CheckboxItem
                                id="thaif-tobogan"
                                checked={thaifTour.toboganRide}
                                onCheckedChange={(checked) => handleThaifChange('toboganRide', checked)}
                                label="Wahana Tobogan"
                            />
                            <CheckboxItem
                                id="thaif-lunch"
                                checked={thaifTour.lunch}
                                onCheckedChange={(checked) => handleThaifChange('lunch', checked)}
                                label="Makan Siang di Thaif"
                            />
                        </div>
                    )}
                </div>

                {/* Haramain Train */}
                <div className="space-y-4 p-4 border border-gray-700 rounded-lg">
                    <h4 className="font-semibold text-white text-lg flex items-center"><Train className="mr-2 h-5 w-5 text-amber-400" /> Tiket Kereta Haramain</h4>
                    <div className="space-y-3">
                        <CheckboxItem
                            id="train-makkah-madinah"
                            checked={routes.makkahToMadinah}
                            onCheckedChange={(checked) => handleCheckboxChange('haramainTrain', 'routes', 'makkahToMadinah', checked)}
                            label="Makkah ➝ Madinah"
                        />
                        <CheckboxItem
                            id="train-madinah-makkah"
                            checked={routes.madinahToMakkah}
                            onCheckedChange={(checked) => handleCheckboxChange('haramainTrain', 'routes', 'madinahToMakkah', checked)}
                            label="Madinah ➝ Makkah"
                        />
                        <CheckboxItem
                            id="train-madinah-jeddah"
                            checked={routes.madinahToJeddah}
                            onCheckedChange={(checked) => handleCheckboxChange('haramainTrain', 'routes', 'madinahToJeddah', checked)}
                            label="Madinah ➝ Jeddah Airport"
                        />
                         <CheckboxItem
                            id="train-jeddah-madinah"
                            checked={routes.jeddahToMadinah}
                            onCheckedChange={(checked) => handleCheckboxChange('haramainTrain', 'routes', 'jeddahToMadinah', checked)}
                            label="Jeddah Airport ➝ Madinah"
                        />
                    </div>
                    {(Object.values(routes).some(v => v)) && (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4">
                            <div className="space-y-2">
                                <Label htmlFor="train-pax" className="flex items-center"><Users className="mr-2 h-4 w-4" /> Jumlah Penumpang</Label>
                                <Input id="train-pax" type="number" placeholder="Jumlah" value={haramainTrain.paxCount} onChange={(e) => handleInputChange('haramainTrain', 'paxCount', e.target.value)} />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="train-time">Preferensi Waktu & Jam</Label>
                                <Input id="train-time" placeholder="Pagi / Siang (14:00)" value={haramainTrain.timePreference} onChange={(e) => handleInputChange('haramainTrain', 'timePreference', e.target.value)} />
                            </div>
                        </div>
                    )}
                </div>
            </CardContent>
        </Card>
    );
};

export default AdditionalProgramsSection;

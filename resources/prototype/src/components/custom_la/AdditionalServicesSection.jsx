
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle, CardDescription } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";

const CheckboxItem = ({ id, checked, onCheckedChange, label }) => (
    <div className="flex items-center space-x-3">
        <Checkbox id={id} checked={checked} onCheckedChange={onCheckedChange} />
        <Label htmlFor={id} className="text-gray-300 font-normal cursor-pointer">{label}</Label>
    </div>
);

const AdditionalServicesSection = ({ formData, handleCheckboxChange, handleInputChange }) => {
    return (
        <Card className="bg-gray-800/50 border-gray-700">
            <CardHeader>
                <CardTitle className="flex items-center">
                    <span className="bg-amber-400 text-black rounded-full h-8 w-8 flex items-center justify-center font-bold text-lg mr-4">H</span>
                    <PERSON><PERSON><PERSON> (Opsional)
                </CardTitle>
                <CardDescription><PERSON>lih layanan opsional untuk melengkapi paket Anda.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
                <div className="space-y-2">
                    <CheckboxItem
                        id="extra-ziarah"
                        checked={formData.extraZiarah.enabled}
                        onCheckedChange={(c) => handleCheckboxChange('extraZiarah', 'enabled', c)}
                        label="Tambahan Ziarah Kota"
                    />
                    {formData.extraZiarah.enabled && (
                        <Input
                            className="ml-8 w-[calc(100%-2rem)]"
                            placeholder="Tulis tempat ziarah, contoh: Museum Al-Amoudi, Makkah"
                            value={formData.extraZiarah.location}
                            onChange={(e) => handleInputChange('extraZiarah', 'location', e.target.value)}
                        />
                    )}
                </div>
                <div className="space-y-2">
                     <CheckboxItem
                        id="extra-meal"
                        checked={formData.extraMeal.enabled}
                        onCheckedChange={(c) => handleCheckboxChange('extraMeal', 'enabled', c)}
                        label="Meal Tambahan"
                    />
                    {formData.extraMeal.enabled && (
                        <Input
                            className="ml-8 w-[calc(100%-2rem)]"
                            placeholder="Contoh: Makan siang Nusantara, 2x"
                            value={formData.extraMeal.details}
                            onChange={(e) => handleInputChange('extraMeal', 'details', e.target.value)}
                        />
                    )}
                </div>
                <CheckboxItem
                    id="pro-documentation"
                    checked={formData.proDocumentation}
                    onCheckedChange={(c) => handleInputChange('proDocumentation', null, c)}
                    label="Dokumentasi Profesional (Foto & Video)"
                />
            </CardContent>
        </Card>
    );
};

export default AdditionalServicesSection;

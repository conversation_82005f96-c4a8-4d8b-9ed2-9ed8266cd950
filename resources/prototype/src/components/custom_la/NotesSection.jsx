import React from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";

const NotesSection = ({ value, handleInputChange }) => {
    return (
        <Card className="bg-gray-800/50 border-gray-700">
            <CardHeader>
                <CardTitle className="flex items-center">
                    <span className="bg-amber-400 text-black rounded-full h-8 w-8 flex items-center justify-center font-bold text-lg mr-4">J</span>
                    Catatan Tambahan
                </CardTitle>
                <CardDescription>Tuliskan informasi atau permintaan lain yang belum tercakup di atas.</CardDescription>
            </CardHeader>
            <CardContent>
                <Textarea
                    placeholder="Contoh: Mohon sediakan 1 kursi roda untuk jamaah lansia..."
                    value={value}
                    onChange={(e) => handleInputChange('notes', e.target.value)}
                    rows={5}
                />
            </CardContent>
        </Card>
    );
};

export default NotesSection;
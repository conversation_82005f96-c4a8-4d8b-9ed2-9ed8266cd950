
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle, CardDescription } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Textarea } from "@/components/ui/textarea";

const CheckboxItem = ({ id, checked, onCheckedChange, label }) => (
    <div className="flex items-center space-x-3">
        <Checkbox id={id} checked={checked} onCheckedChange={onCheckedChange} />
        <Label htmlFor={id} className="text-gray-300 font-normal cursor-pointer">{label}</Label>
    </div>
);

const MutawwifRequestSection = ({ formData, handleInputChange, handleCheckboxChange }) => {
    return (
        <Card className="bg-gray-800/50 border-gray-700">
            <CardHeader>
                <CardTitle className="flex items-center">
                    <span className="bg-amber-400 text-black rounded-full h-8 w-8 flex items-center justify-center font-bold text-lg mr-4">G</span>
                    Permintaan Mutawwif
                </CardTitle>
                <CardDescription>Anda dapat menentukan kriteria Mutawwif yang dibutuhkan.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
                <RadioGroup
                    value={formData.needed}
                    onValueChange={(value) => handleInputChange('needed', value)}
                    className="flex flex-col sm:flex-row gap-4"
                >
                    <div className="flex items-center space-x-2">
                        <RadioGroupItem value="Tidak" id="mutawwif-no" />
                        <Label htmlFor="mutawwif-no">Tidak butuh Mutawwif</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                        <RadioGroupItem value="Ya" id="mutawwif-yes" />
                        <Label htmlFor="mutawwif-yes">Ya, butuh Mutawwif</Label>
                    </div>
                </RadioGroup>

                {formData.needed === 'Ya' && (
                    <div className="space-y-6">
                        <div>
                            <h4 className="font-semibold text-white mb-3">Gaya & Karakteristik</h4>
                            <div className="grid grid-cols-2 gap-3">
                                <CheckboxItem id="style-sunnah" checked={formData.style.sunnah} onCheckedChange={(c) => handleCheckboxChange('mutawwifRequest', 'style', 'sunnah', c)} label="Sesuai Sunnah" />
                                <CheckboxItem id="style-communicative" checked={formData.style.communicative} onCheckedChange={(c) => handleCheckboxChange('mutawwifRequest', 'style', 'communicative', c)} label="Komunikatif & ceria" />
                                <CheckboxItem id="style-calm" checked={formData.style.calm} onCheckedChange={(c) => handleCheckboxChange('mutawwifRequest', 'style', 'calm', c)} label="Tenang & berwibawa" />
                                <CheckboxItem id="style-memorizer" checked={formData.style.memorizer} onCheckedChange={(c) => handleCheckboxChange('mutawwifRequest', 'style', 'memorizer', c)} label="Hafal doa ziarah" />
                                <CheckboxItem id="style-knowledgeable" checked={formData.style.knowledgeable} onCheckedChange={(c) => handleCheckboxChange('mutawwifRequest', 'style', 'knowledgeable', c)} label="Ilmu keislaman mendalam" />
                            </div>
                        </div>
                        <div>
                            <h4 className="font-semibold text-white mb-3">Kemampuan Tambahan</h4>
                            <div className="grid grid-cols-2 gap-3">
                                <CheckboxItem id="skill-photo" checked={formData.skills.photoVideo} onCheckedChange={(c) => handleCheckboxChange('mutawwifRequest', 'skills', 'photoVideo', c)} label="Fotografi / Videografi" />
                                <CheckboxItem id="skill-mc" checked={formData.skills.mc} onCheckedChange={(c) => handleCheckboxChange('mutawwifRequest', 'skills', 'mc', c)} label="Bisa MC / Tour Leader" />
                                <CheckboxItem id="skill-manasik" checked={formData.skills.manasik} onCheckedChange={(c) => handleCheckboxChange('mutawwifRequest', 'skills', 'manasik', c)} label="Pembimbing Manasik" />
                            </div>
                        </div>
                         <div>
                            <h4 className="font-semibold text-white mb-3">Bahasa</h4>
                            <div className="grid grid-cols-2 gap-3">
                                <CheckboxItem id="lang-id" checked={formData.languages.indonesia} onCheckedChange={(c) => handleCheckboxChange('mutawwifRequest', 'languages', 'indonesia', c)} label="Indonesia" />
                                <CheckboxItem id="lang-ar" checked={formData.languages.arabic} onCheckedChange={(c) => handleCheckboxChange('mutawwifRequest', 'languages', 'arabic', c)} label="Arab" />
                                <CheckboxItem id="lang-en" checked={formData.languages.english} onCheckedChange={(c) => handleCheckboxChange('mutawwifRequest', 'languages', 'english', c)} label="Inggris" />
                            </div>
                        </div>
                        <div>
                            <Label htmlFor="mutawwif-notes" className="font-semibold text-white">Catatan Khusus</Label>
                            <Textarea id="mutawwif-notes" value={formData.notes} onChange={(e) => handleInputChange('notes', e.target.value)} placeholder="Contoh: Berpengalaman handle grup besar, usia 30-40 tahun" className="mt-2" />
                        </div>
                    </div>
                )}
            </CardContent>
        </Card>
    );
};

export default MutawwifRequestSection;

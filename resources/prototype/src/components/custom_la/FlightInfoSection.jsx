import React from 'react';
import { format } from "date-fns";
import { id } from "date-fns/locale";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Calendar as CalendarIcon } from 'lucide-react';

const FlightInfoSection = ({ formData, handleInputChange, setFormData, handleDateChange: propHandleDateChange }) => {
    
    const handleDateChange = (field, date) => {
        if (propHandleDateChange) {
            propHandleDateChange(field, date);
        } else {
             setFormData(prev => ({
                ...prev,
                flightInfo: {
                    ...prev.flightInfo,
                    [field]: date
                }
            }));
        }
    };
    
    return (
        <Card className="bg-gray-800/50 border-gray-700">
            <CardHeader>
                <CardTitle className="flex items-center">
                     <span className="bg-amber-400 text-black rounded-full h-8 w-8 flex items-center justify-center font-bold text-lg mr-4">B</span>
                     Jadwal & Penerbangan
                </CardTitle>
                <CardDescription>Informasi mengenai jadwal keberangkatan dan detail penerbangan.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                        <Label>Tanggal Keberangkatan</Label>
                        <Popover>
                            <PopoverTrigger asChild>
                                <Button variant="outline" className="w-full justify-start text-left font-normal">
                                    <CalendarIcon className="mr-2 h-4 w-4" />
                                    {formData.departureDate ? format(new Date(formData.departureDate), "PPP", { locale: id }) : <span>Pilih tanggal</span>}
                                </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0"><Calendar mode="single" selected={formData.departureDate ? new Date(formData.departureDate) : null} onSelect={(date) => handleDateChange('departureDate', date)} initialFocus /></PopoverContent>
                        </Popover>
                    </div>
                    <div className="space-y-2">
                        <Label>Tanggal Kepulangan (Opsional)</Label>
                        <Popover>
                            <PopoverTrigger asChild>
                                <Button variant="outline" className="w-full justify-start text-left font-normal">
                                    <CalendarIcon className="mr-2 h-4 w-4" />
                                    {formData.returnDate ? format(new Date(formData.returnDate), "PPP", { locale: id }) : <span>Pilih tanggal</span>}
                                </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0"><Calendar mode="single" selected={formData.returnDate ? new Date(formData.returnDate) : null} onSelect={(date) => handleDateChange('returnDate', date)} fromDate={formData.departureDate ? new Date(formData.departureDate) : null} /></PopoverContent>
                        </Popover>
                    </div>
                </div>

                <div className="space-y-3">
                    <Label>Status Tiket</Label>
                    <RadioGroup
                        value={formData.ticketStatus}
                        onValueChange={(value) => handleInputChange('ticketStatus', value)}
                        className="flex flex-col sm:flex-row gap-4"
                    >
                        <div className="flex items-center space-x-2">
                            <RadioGroupItem value="Sudah Ada" id="ticket-yes" />
                            <Label htmlFor="ticket-yes">Sudah ada tiket</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                            <RadioGroupItem value="Belum Ada" id="ticket-no" />
                            <Label htmlFor="ticket-no">Belum ada tiket</Label>
                        </div>
                    </RadioGroup>
                </div>
                
                <div className="space-y-4">
                    <div>
                        <h4 className="font-semibold text-white mb-2">Detail Keberangkatan</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                             <div className="space-y-2">
                                <Label htmlFor="departureAirline">Maskapai</Label>
                                <Input
                                    id="departureAirline"
                                    placeholder="Contoh: Saudia Airlines"
                                    value={formData.departureAirline}
                                    onChange={(e) => handleInputChange('departureAirline', e.target.value)}
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="departureFlightNumber">Nomor Penerbangan (Opsional)</Label>
                                <Input
                                    id="departureFlightNumber"
                                    placeholder="Contoh: SV 817"
                                    value={formData.departureFlightNumber}
                                    onChange={(e) => handleInputChange('departureFlightNumber', e.target.value)}
                                />
                            </div>
                        </div>
                    </div>
                     <div>
                        <h4 className="font-semibold text-white mb-2">Detail Kepulangan</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                             <div className="space-y-2">
                                <Label htmlFor="returnAirline">Maskapai</Label>
                                <Input
                                    id="returnAirline"
                                    placeholder="Contoh: Garuda Indonesia"
                                    value={formData.returnAirline}
                                    onChange={(e) => handleInputChange('returnAirline', e.target.value)}
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="returnFlightNumber">Nomor Penerbangan (Opsional)</Label>
                                <Input
                                    id="returnFlightNumber"
                                    placeholder="Contoh: GA 981"
                                    value={formData.returnFlightNumber}
                                    onChange={(e) => handleInputChange('returnFlightNumber', e.target.value)}
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
};

export default FlightInfoSection;
import React, { useState, useContext } from 'react';
import { Button } from '@/components/ui/button.jsx';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card.jsx';
import { Send, MessageCircle, Loader2 } from 'lucide-react';
import { motion } from 'framer-motion';
import { useToast } from '@/components/ui/use-toast.js';
import { LanguageContext } from '@/contexts/LanguageContext.jsx';
import { leadsStorageService } from '@/services/leadsStorageService.js';
import ContactFormInputs from './ContactFormInputs.jsx';

const ContactForm = () => {
  const { toast } = useToast();
  const { translations } = useContext(LanguageContext);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      leadsStorageService.addLead(formData, 'Formulir Kontak Website');

      const whatsappMessage = `Halo! Saya ${formData.name}.\n\nSubjek: ${formData.subject}\n\nPesan: ${formData.message}\n\nKontak:\n- Email: ${formData.email}\n- Telepon: ${formData.phone}`;
      const whatsappUrl = `https://wa.me/6281280908093?text=${encodeURIComponent(whatsappMessage)}`;

      setFormData({ name: '', email: '', phone: '', subject: '', message: '' });

      toast({
        title: "Pesan Berhasil Dikirim!",
        description: "Anda akan diarahkan ke WhatsApp untuk melanjutkan.",
      });

      setTimeout(() => {
        window.open(whatsappUrl, '_blank');
      }, 1000);

    } catch (error) {
      console.error('Error submitting form:', error);
      toast({
        title: "Gagal Mengirim Pesan",
        description: "Terjadi kesalahan. Silakan coba lagi.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleWhatsAppDirect = () => {
    const whatsappUrl = `https://wa.me/6281280908093?text=${encodeURIComponent(translations.whatsappGreeting || 'Assalamualaikum, saya tertarik dengan layanan Umrah Service.')}`;
    window.open(whatsappUrl, '_blank');
  };

  return (
    <Card className="bg-gradient-to-br from-gray-800/80 to-gray-900/70 border-gray-700 text-white shadow-2xl backdrop-blur-sm h-full flex flex-col">
      <CardHeader>
        <CardTitle className="text-3xl font-bold text-center gradient-text">
          {translations.contactFormTitle || 'Kirim Pesan'}
        </CardTitle>
        <CardDescription className="text-gray-400 text-center text-base">
          {translations.contactFormSubtitle || 'Isi formulir di bawah atau langsung chat via WhatsApp untuk respon cepat.'}
        </CardDescription>
      </CardHeader>
      <CardContent className="flex-grow">
        <form onSubmit={handleSubmit} className="space-y-5">
          <ContactFormInputs 
            formData={formData} 
            handleInputChange={handleInputChange} 
          />
          <div className="flex flex-col sm:flex-row gap-4 pt-2">
            <Button 
              type="submit" 
              disabled={isSubmitting}
              className="flex-1 gold-gradient text-black font-semibold text-base py-3 hover:opacity-90 transition-all duration-300 transform hover:scale-105"
            >
              {isSubmitting ? (
                <Loader2 size={20} className="mr-2 animate-spin" />
              ) : (
                <Send size={18} className="mr-2" />
              )}
              {isSubmitting ? (translations.formSending || 'Mengirim...') : (translations.contactFormButtonSend || 'Kirim via Formulir')}
            </Button>
            
            <Button 
              type="button"
              onClick={handleWhatsAppDirect}
              variant="outline"
              className="flex-1 bg-green-600/10 border-green-500/50 text-green-400 hover:bg-green-500/20 hover:text-green-300 font-semibold text-base py-3 transition-all duration-300 transform hover:scale-105"
            >
              <MessageCircle size={18} className="mr-2" />
              {translations.contactWhatsApp || 'Chat di WhatsApp'}
            </Button>
          </div>
        </form>
      </CardContent>
      <CardFooter>
        <p className="text-center text-xs text-gray-500 w-full">
          {translations.formFooterText || 'Tim kami akan merespon pesan Anda secepatnya. Kami beroperasi 24/7 untuk kenyamanan Anda.'}
        </p>
      </CardFooter>
    </Card>
  );
};

export default ContactForm;
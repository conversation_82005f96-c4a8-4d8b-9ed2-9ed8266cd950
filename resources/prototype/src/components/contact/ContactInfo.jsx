import React, { useContext } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card.jsx';
import { Building, MapPin, Phone, Mail, MessageCircle } from 'lucide-react';
import { LanguageContext } from '@/contexts/LanguageContext';

const ContactInfo = () => {
  const { translations } = useContext(LanguageContext);
  const getTranslation = (key, fallback) => translations[key] || fallback;

  const contactDetails = [
    { 
      icon: <Building className="w-7 h-7 text-[#FFD700]" />, 
      titleKey: 'contactIndonesiaOffice', 
      details: [
        { type: 'address', text: 'Grand Galaxy Park, Ruko No. 81 Blok RGA, Jaka Setia, Kec. Bekasi Selatan, Kota Bekasi, Jawa Barat 17147' },
        { type: 'phone', textKey: 'contactPhoneIndonesia', href: `tel:${getTranslation('contactPhoneIndonesia', '+6281280908093').replace(/\s|\(|\)/g, '')}` },
        { type: 'email', textKey: 'contactEmail1', href: `mailto:${getTranslation('contactEmail1', '<EMAIL>')}` },
      ]
    },
    { 
      icon: <MapPin className="w-7 h-7 text-[#FFD700]" />, 
      titleKey: 'contactSaudiOffice', 
      details: [
        { type: 'address', text: 'Al Imam Muslim St., Az Zahir, Makkah Al Mukarramah' },
        { type: 'phone', textKey: 'contactPhoneSaudi', href: `tel:${getTranslation('contactPhoneSaudi', '+966540705271').replace(/\s|\(|\)/g, '')}` },
      ]
    },
  ];

  const InfoItem = ({ icon, children }) => (
    <div className="flex items-start">
      <div className="flex-shrink-0 mt-1 mr-4 text-gray-400">{icon}</div>
      <div className="text-gray-300 text-base">{children}</div>
    </div>
  );

  return (
    <Card className="bg-gradient-to-br from-gray-800/80 to-gray-900/70 border-gray-700 text-white shadow-2xl backdrop-blur-sm">
      <CardHeader>
        <CardTitle className="text-3xl font-bold gradient-text">
          {getTranslation('contactInfoTitle', 'Informasi Kontak')}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-8">
        {contactDetails.map((office, index) => (
          <div key={index}>
            <div className="flex items-center mb-4">
              {office.icon}
              <h4 className="ml-3 font-bold text-xl text-white">{getTranslation(office.titleKey, office.titleKey)}</h4>
            </div>
            <div className="space-y-3 pl-2 border-l-2 border-amber-500/30 ml-3">
              {office.details.map((detail, detailIndex) => (
                <InfoItem 
                  key={detailIndex}
                  icon={
                    detail.type === 'address' ? <MapPin className="w-5 h-5" /> :
                    detail.type === 'phone' ? <Phone className="w-5 h-5" /> :
                    <Mail className="w-5 h-5" />
                  }
                >
                  {detail.href ? (
                    <a href={detail.href} className="hover:text-amber-400 transition-colors duration-300 break-words">
                      {getTranslation(detail.textKey, detail.text || detail.textKey)}
                    </a>
                  ) : (
                    <span className="break-words">{getTranslation(detail.textKey, detail.text || detail.textKey)}</span>
                  )}
                </InfoItem>
              ))}
            </div>
          </div>
        ))}
        
        <div className="mt-6 pt-6 border-t border-gray-700">
          <div className="flex items-center mb-3">
            <MessageCircle className="w-7 h-7 text-green-400" />
            <h4 className="ml-3 font-bold text-xl text-white">{getTranslation('contactInfoFastResponseTitle', 'Butuh Respon Cepat?')}</h4>
          </div>
          <p className="text-gray-400 mb-4 pl-10">
            {getTranslation('contactInfoFastResponseDesc', 'Hubungi kami langsung via WhatsApp untuk konsultasi real-time.')}
          </p>
          <a 
            href={`${getTranslation('socialWhatsAppUrl', 'https://wa.me/6281280908093')}?text=${encodeURIComponent(getTranslation('whatsappGreeting', 'Assalamualaikum, saya tertarik dengan layanan Umrah Service.'))}`}
            target="_blank"
            rel="noopener noreferrer"
            className="ml-10 inline-flex items-center px-6 py-2.5 bg-green-600 hover:bg-green-500 text-white rounded-lg transition-colors text-base font-semibold transform hover:scale-105"
          >
            <MessageCircle className="w-5 h-5 mr-2.5" />
            {getTranslation('contactInfoChatOnWhatsApp', 'Chat di WhatsApp')}
          </a>
        </div>
      </CardContent>
    </Card>
  );
};

export default ContactInfo;
import React, { useState, useContext } from 'react';
import { Button } from '@/components/ui/button.jsx';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card.jsx';
import { Calendar as CalendarIcon, User, Mail, Phone, Clock, FileText, Loader2, CalendarPlus } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast.js';
import { appointmentStorageService } from '@/services/appointmentStorageService.js';
import { Input } from '@/components/ui/input.jsx';
import { Label } from '@/components/ui/label.jsx';
import { Textarea } from '@/components/ui/textarea.jsx';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover.jsx';
import { Calendar } from '@/components/ui/calendar.jsx';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select.jsx';
import { cn } from '@/lib/utils.js';
import { format } from 'date-fns';
import { id } from 'date-fns/locale';
import { LanguageContext } from '@/contexts/LanguageContext.jsx';

const AppointmentForm = () => {
  const { toast } = useToast();
  const { translations } = useContext(LanguageContext);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    notes: ''
  });
  const [date, setDate] = useState(null);
  const [time, setTime] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleTimeChange = (value) => {
    setTime(value);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!date || !time) {
      toast({
        title: "Data Tidak Lengkap",
        description: "Silakan pilih tanggal dan waktu janji temu.",
        variant: "destructive",
      });
      return;
    }
    setIsSubmitting(true);
    
    try {
      const appointmentData = {
        ...formData,
        date: format(date, 'yyyy-MM-dd'),
        time,
      };
      appointmentStorageService.addAppointment(appointmentData);

      const whatsappMessage = `*PENGATURAN JANJI TEMU BARU*

Halo Tim Arrahmah,

Saya ingin mengatur janji temu dengan detail sebagai berikut:
- *Nama:* ${formData.name}
- *Email:* ${formData.email}
- *Telepon:* ${formData.phone}
- *Tanggal:* ${format(date, 'EEEE, dd MMMM yyyy', { locale: id })}
- *Waktu:* ${time}
- *Catatan:* ${formData.notes || 'Tidak ada catatan tambahan.'}

Mohon konfirmasinya. Terima kasih.
`;
      const whatsappUrl = `https://wa.me/6281280908093?text=${encodeURIComponent(whatsappMessage)}`;
      
      toast({
        title: "Permintaan Janji Temu Terkirim!",
        description: "Anda akan diarahkan ke WhatsApp untuk konfirmasi.",
      });

      setFormData({ name: '', email: '', phone: '', notes: '' });
      setDate(null);
      setTime('');

      setTimeout(() => {
        window.open(whatsappUrl, '_blank');
      }, 1000);

    } catch (error) {
      console.error('Error submitting appointment:', error);
      toast({
        title: "Gagal Mengirim Permintaan",
        description: "Terjadi kesalahan. Silakan coba lagi.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  const timeSlots = [
    '09:00 - 10:00', '10:00 - 11:00', '11:00 - 12:00',
    '13:00 - 14:00', '14:00 - 15:00', '15:00 - 16:00', '16:00 - 17:00'
  ];

  const inputFields = [
    { id: 'name', label: 'Nama Lengkap', type: 'text', placeholder: 'Nama Anda', icon: User },
    { id: 'email', label: 'Alamat Email', type: 'email', placeholder: '<EMAIL>', icon: Mail },
    { id: 'phone', label: 'Nomor Telepon (WhatsApp)', type: 'tel', placeholder: '+62 812...', icon: Phone },
  ];

  return (
    <Card className="bg-gradient-to-br from-gray-800/80 to-gray-900/70 border-gray-700 text-white shadow-2xl backdrop-blur-sm h-full flex flex-col">
      <CardHeader>
        <CardTitle className="text-3xl font-bold text-center gradient-text">{translations.appointmentFormTitle || 'Buat Janji Temu'}</CardTitle>
        <CardDescription className="text-gray-400 text-center text-base">{translations.appointmentFormSubtitle || 'Atur jadwal pertemuan virtual atau tatap muka dengan tim kami.'}</CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-5">
          {inputFields.map(({ id, label, type, placeholder, icon: Icon }) => (
            <div key={id}>
              <Label htmlFor={id} className="text-gray-300 mb-2 block font-medium">{label}</Label>
              <div className="relative">
                <Icon className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
                <Input type={type} name={id} id={id} value={formData[id]} onChange={handleInputChange} required className="bg-gray-900/50 border-gray-700 text-white placeholder-gray-500 focus:border-amber-400 focus:ring-amber-400 pl-10 py-3 h-12 text-base" placeholder={placeholder} />
              </div>
            </div>
          ))}

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-5">
            <div>
              <Label htmlFor="date" className="text-gray-300 mb-2 block font-medium">Tanggal</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant={"outline"} className={cn("w-full justify-start text-left font-normal h-12 text-base bg-gray-900/50 border-gray-700 hover:bg-gray-800 hover:text-white", !date && "text-gray-400")}>
                    <CalendarIcon className="mr-2 h-5 w-5" />
                    {date ? format(date, "PPP", { locale: id }) : <span>Pilih tanggal</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0 bg-gray-900 border-gray-700 text-white">
                  <Calendar mode="single" selected={date} onSelect={setDate} initialFocus disabled={(day) => day < new Date(new Date().setDate(new Date().getDate() - 1)) || day.getDay() === 0 || day.getDay() === 6} />
                </PopoverContent>
              </Popover>
            </div>
            <div>
              <Label htmlFor="time" className="text-gray-300 mb-2 block font-medium">Waktu (WIB)</Label>
              <Select onValueChange={handleTimeChange} value={time}>
                <SelectTrigger className="w-full h-12 text-base bg-gray-900/50 border-gray-700 text-white focus:border-amber-400 focus:ring-amber-400">
                  <Clock className="mr-2 h-5 w-5 text-gray-400 inline-block" />
                  <SelectValue placeholder="Pilih waktu" />
                </SelectTrigger>
                <SelectContent className="bg-gray-900 border-gray-700 text-white">
                  {timeSlots.map(slot => <SelectItem key={slot} value={slot}>{slot}</SelectItem>)}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="notes" className="text-gray-300 mb-2 block font-medium">Topik / Catatan</Label>
            <div className="relative">
              <FileText className="absolute left-3 top-3.5 h-5 w-5 text-gray-400" />
              <Textarea name="notes" id="notes" rows={4} value={formData.notes} onChange={handleInputChange} className="bg-gray-900/50 border-gray-700 text-white placeholder-gray-500 focus:border-amber-400 focus:ring-amber-400 resize-none text-base pl-10 p-3" placeholder="Contoh: Diskusi paket custom untuk 100 jamaah" />
            </div>
          </div>

          <div className="pt-2">
            <Button type="submit" disabled={isSubmitting} className="w-full gold-gradient text-black font-semibold text-base py-3 hover:opacity-90 transition-all duration-300 transform hover:scale-105">
              {isSubmitting ? <Loader2 size={20} className="mr-2 animate-spin" /> : <CalendarPlus size={18} className="mr-2" />}
              {isSubmitting ? 'Mengirim...' : 'Atur Janji Temu'}
            </Button>
          </div>
        </form>
      </CardContent>
      <CardFooter>
        <p className="text-center text-xs text-gray-500 w-full">{translations.appointmentFooterText || 'Tim kami akan mengkonfirmasi jadwal Anda melalui WhatsApp atau Email.'}</p>
      </CardFooter>
    </Card>
  );
};

export default AppointmentForm;
import React, { useContext } from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle, CardDescription } from '@/components/ui/card.jsx';
import { MapPin } from 'lucide-react';
import { LanguageContext } from '@/contexts/LanguageContext';

const ContactMap = () => {
  const { translations } = useContext(LanguageContext);
  const getTranslation = (key, fallback) => translations[key] || fallback;

  return (
    <Card className="bg-gradient-to-br from-gray-800/80 to-gray-900/70 border-gray-700 text-white shadow-2xl backdrop-blur-sm">
      <CardHeader>
        <div className="flex items-center">
          <MapPin className="w-7 h-7 text-[#FFD700]" />
          <CardTitle className="ml-3 text-3xl font-bold gradient-text">{getTranslation('contactMapTitle', 'Lokasi Kami')}</CardTitle>
        </div>
        <CardDescription className="text-gray-400 pt-2 text-base">
          {getTranslation('contactMapSubtitle', 'Kunjungi kantor kami di Indonesia untuk diskusi lebih lanjut.')}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="rounded-lg overflow-hidden shadow-lg border border-gray-700/50">
          <iframe 
            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3965.7473!2d106.9710844!3d-6.2740547!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x2e698d7853483f65%3A0x91ec02a1f73174a6!2sArrahmah%20Umrah%20Handling!5e0!3m2!1sen!2sid!4v1735334400000!5m2!1sen!2sid"
            width="100%" 
            height="350" 
            style={{ border:0 }} 
            allowFullScreen="" 
            loading="lazy" 
            referrerPolicy="no-referrer-when-downgrade"
            title={getTranslation('contactMapTitle', 'Lokasi Kantor Arrahmah Umrah Handling Indonesia')}>
          </iframe>
        </div>
      </CardContent>
    </Card>
  );
};

export default ContactMap;
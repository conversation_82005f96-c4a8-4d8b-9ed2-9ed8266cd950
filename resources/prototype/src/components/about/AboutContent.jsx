import React, { useContext } from 'react';
import { motion } from 'framer-motion';
import { Target, BookOpen, Award, ShieldCheck, Layers, Smartphone, Network, BadgePercent } from 'lucide-react';
import { LanguageContext } from '@/contexts/LanguageContext';

const AboutContent = () => {
  const { translations } = useContext(LanguageContext);
  const getTranslation = (key, fallback) => translations[key] || fallback;

  const newAdvantages = [
    { icon: <Award className="w-8 h-8 text-[#FFD700]" />, titleKey: "advantage1Title", descriptionKey: "advantage1Desc", taglineKey: "advantage1Tagline" },
    { icon: <ShieldCheck className="w-8 h-8 text-[#FFD700]" />, titleKey: "advantage2Title", descriptionKey: "advantage2Desc", taglineKey: "advantage2Tagline" },
    { icon: <Layers className="w-8 h-8 text-[#FFD700]" />, titleKey: "advantage3Title", descriptionKey: "advantage3Desc", taglineKey: "advantage3Tagline" },
    { icon: <Smartphone className="w-8 h-8 text-[#FFD700]" />, titleKey: "advantage4Title", descriptionKey: "advantage4Desc", taglineKey: "advantage4Tagline" },
    { icon: <Network className="w-8 h-8 text-[#FFD700]" />, titleKey: "advantage5Title", descriptionKey: "advantage5Desc", taglineKey: "advantage5Tagline" },
    { icon: <BadgePercent className="w-8 h-8 text-[#FFD700]" />, titleKey: "advantage6Title", descriptionKey: "advantage6Desc", taglineKey: "advantage6Tagline" }
  ];

  const fadeIn = (delay = 0) => ({
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.6, delay } }
  });

  const managementTeamImageUrl = "https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/fd4aff727e95640a450fd24b2a24873d.jpg";

  return (
    <div className="py-16 bg-background text-gray-300">
      <div className="container mx-auto px-4 md:px-6 space-y-16">
        
        <motion.section
          variants={fadeIn()}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
          className="text-center"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4 gradient-text">
            {getTranslation('aboutWelcomeTitle', "Selamat Datang di Umrahservice.co")}
          </h2>
          <p className="text-xl md:text-2xl font-semibold text-amber-400 mb-8">
            {getTranslation('aboutWelcomeSubtitle', "Mitra Handling Umrah & Haji Profesional, Legal, dan Terpercaya")}
          </p>
          <div className="text-left max-w-4xl mx-auto space-y-4 text-lg leading-relaxed">
            <p>
              {getTranslation('aboutIntroPara1', "Arrahmah Handling Service adalah perusahaan penyedia layanan ground handling dan Land Arrangement (LA) Umrah & Haji, yang berbasis di Makkah Al-Mukarramah, Arab Saudi dan Indonesia. Kami hadir untuk memberikan kemudahan, kenyamanan, dan ketenangan dalam setiap perjalanan ibadah, melalui layanan profesional, terintegrasi, dan sepenuhnya legal.")}
            </p>
            <p>
              {getTranslation('aboutIntroPara2', "Dengan pengalaman lebih dari 8 tahun, kami telah dipercaya oleh berbagai biro perjalanan haji dan umrah dalam menangani lebih dari 5.000 jamaah pada musim Umrah 1446 H, dan menargetkan hingga 10.000 jamaah pada musim 1447 H dengan standar pelayanan yang lebih tinggi dan sistem yang lebih efisien.")}
            </p>
            <p>
              {getTranslation('aboutIntroPara3', "Arrahmah didukung penuh oleh Muassasah Resmi Kerajaan Arab Saudi, dan telah terdaftar secara legal dengan Nomor Registrasi 10011425. Kredibilitas kami juga dikukuhkan melalui keikutsertaan dalam Forum Umrah & Ziyarah Internasional di Madinah.")}
            </p>
            <p className="font-semibold text-amber-300 pt-4">
              {"🌐 " + (getTranslation('aboutIntroHighlight', "Kami siap menjadi mitra terbaik Anda dalam menyediakan layanan handling, LA, akomodasi, visa, transportasi, katering, hingga pendampingan ibadah — baik untuk travel skala kecil, besar, maupun jamaah mandiri."))}
            </p>
          </div>
        </motion.section>

        <motion.section
          variants={fadeIn(0.15)}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.2 }}
          className="my-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-12 text-center gradient-text">
            {getTranslation('managementTeamTitle', "Tim Manajemen Kami")}
          </h2>
          <div className="bg-secondary p-6 md:p-8 rounded-xl shadow-2xl border border-gray-700/60 flex justify-center items-center">
            <img
              src={managementTeamImageUrl}
              alt={getTranslation('managementTeamAlt', "Tim Manajemen Arrahmah Handling Service")}
              className="w-full max-w-4xl h-auto rounded-lg shadow-lg object-contain"
            />
          </div>
        </motion.section>


        <motion.section
          variants={fadeIn(0.2)}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
          className="bg-secondary p-8 md:p-10 rounded-xl shadow-xl border border-gray-700/60"
        >
          <div className="flex flex-col md:flex-row items-center md:items-start">
            <BookOpen className="w-16 h-16 text-[#FFD700] mb-6 md:mb-0 md:mr-8 flex-shrink-0" />
            <div>
              <h2 className="text-3xl font-bold text-white mb-4">
                {getTranslation('aboutMeaningTitle', "Makna “Arrahmah”")}
              </h2>
              <p className="text-lg leading-relaxed">
                {getTranslation('aboutMeaningDesc', "“Arrahmah” berarti rahmat dalam bahasa Arab, dan menjadi filosofi dasar kami dalam melayani para tamu Allah. Kami percaya bahwa dengan kasih sayang, keikhlasan, dan profesionalisme, setiap perjalanan ibadah akan lebih bermakna, aman, dan penuh ketenangan.")}
              </p>
            </div>
          </div>
        </motion.section>

        <motion.section
          variants={fadeIn(0.3)}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
        >
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6 text-center gradient-text">
            {getTranslation('aboutMottoTitle', "Motto Kami: “Tidur Nyenyak”")}
          </h2>
          <p className="text-lg leading-relaxed text-center max-w-3xl mx-auto">
            {getTranslation('aboutMottoDesc', "Motto ini bukan sekadar slogan, melainkan wujud dari komitmen kami. Dengan mempercayakan jamaah Anda kepada Arrahmah Handling Service, para pemilik travel dapat tidur nyenyak, tanpa khawatir akan operasional di lapangan. Karena kami menangani seluruh detail—mulai dari visa, akomodasi, transportasi, hingga monitoring selama ibadah—dengan penuh tanggung jawab dan dedikasi.")}
          </p>
        </motion.section>

        <motion.section 
          variants={fadeIn(0.4)}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.2 }}
          className="bg-secondary p-8 md:p-10 rounded-xl shadow-xl border border-gray-700/60"
        >
          <div className="flex flex-col md:flex-row items-center md:items-start mb-8">
            <Target className="w-16 h-16 text-[#FFD700] mb-6 md:mb-0 md:mr-8 flex-shrink-0" />
            <div>
                <h2 className="text-3xl font-bold text-white mb-4">
                  {getTranslation('aboutVisionMissionTitle', "Visi dan Misi")}
                </h2>
            </div>
          </div>
          <div className="grid md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-2xl font-semibold text-[#FFD700] mb-3">{getTranslation('aboutVisionTitle', "Visi")}</h3>
              <p className="text-lg leading-relaxed">
                {getTranslation('aboutVisionDesc', "Menjadi pionir layanan handling haji dan umrah terkemuka secara global yang dikenal atas keunggulan layanan, inovasi teknologi, dan komitmen terhadap kepuasan jamaah.")}
              </p>
            </div>
            <div>
              <h3 className="text-2xl font-semibold text-[#FFD700] mb-3">{getTranslation('aboutMissionTitle', "Misi")}</h3>
              <ul className="list-disc list-inside space-y-2 text-lg leading-relaxed">
                <li>{getTranslation('aboutMissionPoint1', "Menyediakan layanan perjalanan ibadah yang terintegrasi, inovatif, dan sesuai dengan regulasi terbaru.")}</li>
                <li>{getTranslation('aboutMissionPoint2', "Menjaga kenyamanan, keamanan, dan ketenangan jamaah melalui pelayanan yang profesional dan humanis.")}</li>
                <li>{getTranslation('aboutMissionPoint3', "Membangun kemitraan strategis dengan berbagai pihak untuk menciptakan nilai tambah.")}</li>
                <li>{getTranslation('aboutMissionPoint4', "Berkontribusi aktif dalam pengembangan industri pariwisata religi yang berkelanjutan.")}</li>
              </ul>
            </div>
          </div>
        </motion.section>

        <motion.section
          variants={fadeIn(0.5)}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
        >
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-12 text-center gradient-text">
            {getTranslation('aboutAdvantagesTitle', '6 Keunggulan Arrahmah Handling Service')}
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
            {newAdvantages.map((advantage, index) => (
              <motion.div 
                key={index}
                className="bg-gradient-to-br from-gray-800 to-gray-900 p-6 rounded-xl shadow-xl border border-gray-700/50 flex items-start space-x-4 transition-all duration-300 hover:shadow-glow hover:border-[#FFD700]/50 transform hover:-translate-y-1"
                variants={fadeIn(0.5 + index * 0.1)}
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
              >
                <div className="flex-shrink-0 mt-1">{advantage.icon}</div>
                <div>
                  <h4 className="text-xl font-semibold text-white mb-2">
                    {getTranslation(advantage.titleKey, advantage.titleKey)}
                  </h4>
                  <p className="text-gray-400 text-sm leading-relaxed mb-3">
                    {getTranslation(advantage.descriptionKey, advantage.descriptionKey)}
                  </p>
                  <p className="text-amber-400/80 text-xs italic">
                    {getTranslation(advantage.taglineKey, advantage.taglineKey)}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.section>

      </div>
    </div>
  );
};

export default AboutContent;
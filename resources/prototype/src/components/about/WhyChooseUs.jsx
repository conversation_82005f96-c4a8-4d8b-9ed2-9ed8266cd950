import React, { useContext } from 'react';
import { motion } from 'framer-motion';
import { CheckCircle2, ShieldCheck, Users, Zap, Award, Smile } from 'lucide-react';
import { LanguageContext } from '@/contexts/LanguageContext';

const WhyChooseUs = () => {
  const { translations } = useContext(LanguageContext);
  const getTranslation = (key, fallback) => translations[key] || fallback;

  const reasons = [
    {
      icon: <CheckCircle2 className="w-8 h-8 text-amber-400" />,
      title: getTranslation('reason1Title', '<PERSON><PERSON>, Se<PERSON>a <PERSON>'),
      description: getTranslation('reason1Desc', 'Dari visa, tiket, hotel, katering, transportasi, hingga mutawwif — cukup satu mitra. Semua kebutuhan <PERSON> & Haji travel Anda kami tangani secara terintegrasi.')
    },
    {
      icon: <ShieldCheck className="w-8 h-8 text-amber-400" />,
      title: getTranslation('reason2Title', 'Legal & Terpercaya'),
      description: getTranslation('reason2Desc', 'Ka<PERSON> bukan hanya berpeng<PERSON> — tapi juga resmi. Terdaftar di Muassasah Arab Saudi (No. 10011425). Legalitas Anda, reputasi kami.')
    },
    {
      icon: <Users className="w-8 h-8 text-amber-400" />,
      title: getTranslation('reason3Title', 'Tim Profesional di Dua Negara'),
      description: getTranslation('reason3Desc', 'Didukung tim solid di Indonesia dan Arab Saudi. Jamaah Anda disambut dan dilayani dari awal hingga pulang, tanpa celah.')
    },
    {
      icon: <Zap className="w-8 h-8 text-amber-400" />,
      title: getTranslation('reason4Title', 'Respons Cepat, Koordinasi Mudah'),
      description: getTranslation('reason4Desc', 'Kami paham ritme dunia travel. Koordinasi cepat, komunikasi efisien, dan dukungan 24 jam — langsung dengan tim lapangan, bukan call center.')
    },
    {
      icon: <Award className="w-8 h-8 text-amber-400" />,
      title: getTranslation('reason5Title', 'Harga Bersahabat, Kualitas Premium'),
      description: getTranslation('reason5Desc', 'Layanan bintang lima, harga bersahabat. Kami punya akses langsung ke vendor hotel, bus, katering, dan mutawwif — tanpa perantara.')
    },
    {
      icon: <Smile className="w-8 h-8 text-amber-400" />,
      title: getTranslation('reason6Title', 'Fokus Kami: Memberikan Anda Ketentraman'),
      description: getTranslation('reason6Desc', 'Kami hadir agar Anda bisa fokus jualan dan pelayanan jamaah, tanpa pusing teknis. Travel Anda, tenang. Jamaah Anda, senang.')
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 100,
      },
    },
  };

  return (
    <section className="py-20 bg-secondary text-white">
      <div className="container mx-auto px-4 md:px-6">
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-3 text-white gradient-text">
            {getTranslation('whyChooseUsTitle', 'Alasan Kenapa Travel Harus Memilih Kami')}
          </h2>
          <div className="w-24 h-1.5 bg-gradient-to-r from-amber-400 to-yellow-500 mx-auto rounded-full"></div>
        </motion.div>

        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.2 }}
        >
          {reasons.map((reason, index) => (
            <motion.div
              key={index}
              className="bg-gray-800/50 border border-gray-700/60 rounded-xl p-6 flex flex-col items-center text-center hover:bg-gray-800 transition-colors duration-300"
              variants={itemVariants}
            >
              <div className="bg-gray-900 p-4 rounded-full mb-4">
                {reason.icon}
              </div>
              <h3 className="text-xl font-semibold text-amber-400 mb-2">{reason.title}</h3>
              <p className="text-gray-400 leading-relaxed">{reason.description}</p>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
};

export default WhyChooseUs;
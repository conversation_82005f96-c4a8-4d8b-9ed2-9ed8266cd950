import React, { useState, useEffect, useContext } from 'react';
    import { Link, useLocation } from 'react-router-dom';
    import { motion, AnimatePresence } from 'framer-motion';
    import { Menu, X, Globe, Instagram, Twitter, ShoppingCart } from 'lucide-react';
    import { But<PERSON> } from '@/components/ui/button';
    import { LanguageContext } from '@/contexts/LanguageContext';
    import {
      DropdownMenu,
      DropdownMenuContent,
      DropdownMenuItem,
      DropdownMenuTrigger,
    } from "@/components/ui/dropdown-menu";

    const WhatsAppIcon = ({ className = "h-5 w-5" }) => (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className={className}>
        <path d="M16.6,14.2l-1.5-0.7c-0.2-0.1-0.5-0.1-0.7,0.1l-0.8,0.9c-0.2,0.2-0.5,0.3-0.8,0.2C11.6,14,10,12.5,9.2,11.2 c-0.1-0.2-0.1-0.5,0.1-0.7l0.7-0.7c0.2-0.2,0.2-0.5,0.1-0.7l-0.7-1.5C8.3,7.4,8,7.3,7.8,7.3L6.2,7.3C5.7,7.3,5.3,7.7,5.3,8.1 c0,0.1,0,0.2,0,0.3c0.4,2.5,1.8,4.7,3.9,6.5c2,1.8,4.3,2.9,6.9,3.1c0.1,0,0.2,0,0.3,0c0.5,0,0.8-0.4,0.8-0.8l0-1.6 C17,14.5,16.8,14.3,16.6,14.2z M12,2C6.5,2,2,6.5,2,12s4.5,10,10,10c5.5,0,10-4.5,10-10S17.5,2,12,2z M12,20.5 c-4.7,0-8.5-3.8-8.5-8.5S7.3,3.5,12,3.5s8.5,3.8,8.5,8.5S16.7,20.5,12,20.5z"/>
      </svg>
    );

    const TikTokIcon = ({ className = "h-5 w-5" }) => (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className={className}>
        <path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-2.47.03-4.8-.73-6.56-2.34-1.45-1.34-2.28-3.05-2.48-4.9-.06-1.72.02-3.44-.02-5.16-.04-1.58-.48-3.16-1.2-4.57-.88-1.7-.49-3.73.8-5.09 1.34-1.42 3.11-2.12 5.04-2.04.83.03 1.66.03 2.48.03Zm0 2.79c-.81 0-1.61.01-2.42.01-1.09.01-2.18.31-3.14.85-.53.29-.93.7-1.22 1.18-.29.47-.44.99-.52 1.52-.07.52-.02 1.05-.02 1.57 0 2.83-.01 5.66-.01 8.49.01.82.21 1.63.55 2.37.47 1.04 1.25 1.86 2.22 2.44.99.59 2.12.88 3.28.83.97-.04 1.92-.34 2.76-.87.53-.33.96-.76 1.3-1.25.29-.41.51-.85.66-1.31.17-.53.28-1.08.32-1.64.04-.6.02-1.2.02-1.81.01-2.82 0-5.65.01-8.48Zm3.86 1.44c-.02.02-.02.02 0 0Z"/>
      </svg>
    );

    const FacebookIcon = ({ className = "h-5 w-5" }) => (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className={className}>
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10s10-4.48 10-10S17.52 2 12 2zm3.5 8h-2.5V8.5c0-.55.45-1 1-1h1.5V5h-2.5C10.56 5 9 6.57 9 8.5v1.5H7v2h2v6h3v-6h2l.5-2z"/>
      </svg>
    );


    const Navbar = () => {
      const [isOpen, setIsOpen] = useState(false);
      const [scrolled, setScrolled] = useState(false);
      const location = useLocation();
      const { language, changeLanguage, translations } = useContext(LanguageContext);

      const logoUrlFromStorage = localStorage.getItem('siteLogo') || "https://storage.googleapis.com/hostinger-horizons-assets-prod/8ffed285-a801-4042-b421-5c69541a2c9d/e2226538273674d2415bfb7f2ef1cba1.png";

      useEffect(() => {
        const handleScroll = () => {
          setScrolled(window.scrollY > 20);
        };

        window.addEventListener('scroll', handleScroll);
        handleScroll(); 
        return () => window.removeEventListener('scroll', handleScroll);
      }, []);

      useEffect(() => {
        document.documentElement.lang = language;
        document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr';
      }, [language]);

      const toggleMenu = () => setIsOpen(!isOpen);
      const closeMenu = () => setIsOpen(false);

      const isActive = (path) => location.pathname === path;

      const navLinks = [
        { nameKey: 'home', path: '/' },
        { 
          nameKey: 'services', 
          isDropdown: true,
          children: [
            { nameKey: 'services', path: '/services' },
            { nameKey: 'Land Arrangement', path: '/land-arrangement-umrah' },
          ]
        },
        { nameKey: 'pricing', path: '/pricing' },
        { nameKey: 'about', path: '/about' },
        { nameKey: 'Blog', path: '/blog' },
        { nameKey: 'faq', path: '/faq' },
        { nameKey: 'contact', path: '/contact' },
        { nameKey: 'bookNow', path: '/order', isCallToAction: true },
      ];

      const socialMediaLinks = [
        { icon: <WhatsAppIcon className="h-5 w-5" />, href: translations.socialWhatsAppUrl || "https://wa.me/6281280908093", label: "WhatsApp" },
        { icon: <Instagram className="h-5 w-5" />, href: translations.socialInstagramUrl || "https://www.instagram.com/umrohserviceco/", label: "Instagram" },
        { icon: <FacebookIcon className="h-5 w-5" />, href: translations.socialFacebookUrl || "https://facebook.com/umrohserviceco/", label: "Facebook" },
        { icon: <TikTokIcon className="h-5 w-5" />, href: translations.socialTikTokUrl || "https://www.tiktok.com/@umrahserviceco", label: "TikTok" },
        { icon: <Twitter className="h-5 w-5" />, href: translations.socialTwitterUrl || "https://x.com/umrahserviceco", label: "Twitter/X" },
      ];
      
      const handleLoginTravelClick = () => {
        window.open(translations.loginTravelUrl || 'https://travel.umrahservice.co', '_blank', 'noopener,noreferrer');
        closeMenu();
      };

      const getTranslatedText = (key, fallbackText = '') => {
        return translations[key] || fallbackText || key;
      };

      return (
        <header 
          className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
            scrolled || isOpen ? 'bg-background/95 backdrop-blur-sm shadow-md' : 'bg-transparent'
          }`}
        >
          <div className="container mx-auto px-4 md:px-6">
            <div className="flex items-center justify-between h-20 md:h-24">
              <Link to="/" className="flex items-center flex-shrink-0" onClick={closeMenu}>
                <img src={logoUrlFromStorage} alt={getTranslatedText('logoAlt', 'Arrahmah Handling Service Logo')} className="h-14 md:h-16 w-auto" />
              </Link>

              <div className="flex items-center">
                <nav className="hidden lg:flex items-center space-x-1 whitespace-nowrap">
                  {navLinks.map((link) => {
                    if (link.isCallToAction) {
                      return (
                         <Button key={link.path} asChild className="ml-4 gold-gradient text-black font-medium">
                          <Link to={link.path} onClick={closeMenu}>
                            <ShoppingCart className="mr-2 h-4 w-4" />
                            {getTranslatedText(link.nameKey, 'Pesan Sekarang')}
                          </Link>
                        </Button>
                      )
                    }
                    if (link.isDropdown) {
                      return (
                        <DropdownMenu key={link.nameKey}>
                          <DropdownMenuTrigger asChild>
                            <button className={`px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ease-in-out
                                ${link.children.some(child => isActive(child.path)) 
                                  ? 'text-[#FFD700] bg-white/10' 
                                  : 'text-white hover:text-[#FFD700]/80 hover:bg-white/5'
                                }`
                            }>
                              {getTranslatedText(link.nameKey)}
                            </button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="start" className="bg-secondary text-white border-border w-48">
                            {link.children.map(child => (
                              <DropdownMenuItem key={child.path} asChild className="hover:bg-primary/20 focus:bg-primary/20 cursor-pointer">
                                <Link to={child.path} onClick={closeMenu}>
                                  {getTranslatedText(child.nameKey)}
                                </Link>
                              </DropdownMenuItem>
                            ))}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      )
                    }
                    return (
                      <Link
                        key={link.path}
                        to={link.path}
                        onClick={closeMenu}
                        className={`px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ease-in-out
                          ${isActive(link.path) 
                            ? 'text-[#FFD700] bg-white/10' 
                            : 'text-white hover:text-[#FFD700]/80 hover:bg-white/5'
                          }`
                        }
                      >
                        {getTranslatedText(link.nameKey)}
                      </Link>
                    )
                  })}
                </nav>

                <div className="hidden lg:block ml-4 flex-shrink-0">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="text-white hover:text-[#FFD700]">
                        <Menu className="h-5 w-5" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="bg-secondary text-white border-border w-48">
                      {socialMediaLinks.map(social => (
                        <DropdownMenuItem key={social.label} asChild className="hover:bg-primary/20 focus:bg-primary/20 cursor-pointer">
                          <a
                            href={social.href}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center w-full"
                          >
                            {React.cloneElement(social.icon, { className: "h-4 w-4 mr-2" })}
                            <span>{social.label}</span>
                          </a>
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>


                <div className="flex-shrink-0">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="ml-2 text-white hover:text-[#FFD700]">
                        <Globe className="h-5 w-5" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align={language === 'ar' ? 'start' : 'end'} className="bg-secondary text-white border-border">
                      <DropdownMenuItem onClick={() => { changeLanguage('id'); closeMenu(); }} className="hover:bg-primary/20 focus:bg-primary/20 cursor-pointer">
                        Bahasa Indonesia
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => { changeLanguage('en'); closeMenu(); }} className="hover:bg-primary/20 focus:bg-primary/20 cursor-pointer">
                        English
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => { changeLanguage('ar'); closeMenu(); }} className="hover:bg-primary/20 focus:bg-primary/20 cursor-pointer">
                        العربية
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>

                <button
                  className="lg:hidden text-white p-2 ml-2 flex-shrink-0"
                  onClick={toggleMenu}
                  aria-label={isOpen ? getTranslatedText('closeMenu', 'Close menu') : getTranslatedText('openMenu', 'Open menu')}
                >
                  {isOpen ? <X size={28} /> : <Menu size={28} />}
                </button>
              </div>
            </div>
          </div>

          <AnimatePresence>
            {isOpen && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
                className="lg:hidden bg-background/95 backdrop-blur-sm absolute top-full left-0 right-0 shadow-lg"
              >
                <div className="container mx-auto px-4 py-4">
                  <nav className="flex flex-col space-y-3">
                    {navLinks.map((link) => {
                      if (link.isCallToAction) {
                         return (
                          <Button key={link.path} asChild className="mt-3 gold-gradient text-black font-medium w-full">
                            <Link to={link.path} onClick={closeMenu}>
                              <ShoppingCart className="mr-2 h-4 w-4" />
                              {getTranslatedText(link.nameKey, 'Pesan Sekarang')}
                            </Link>
                          </Button>
                        )
                      }
                      if (link.isDropdown) {
                        return (
                          <div key={link.nameKey}>
                            <div className="ml-4 flex flex-col space-y-2 border-l-2 border-gray-700 pl-4">
                              <span className="block px-3 py-2 text-base font-medium text-white">{getTranslatedText(link.nameKey)}</span>
                              {link.children.map(child => (
                                <Link
                                  key={child.path}
                                  to={child.path}
                                  className={`block px-3 py-2 rounded-md text-base font-medium
                                    ${isActive(child.path) 
                                      ? 'text-[#FFD700] bg-white/10' 
                                      : 'text-white hover:text-[#FFD700]/80 hover:bg-white/5'
                                    }`
                                  }
                                  onClick={closeMenu}
                                >
                                  {getTranslatedText(child.nameKey)}
                                </Link>
                              ))}
                            </div>
                          </div>
                        )
                      }
                      return (
                        <Link
                          key={link.path}
                          to={link.path}
                          className={`block px-3 py-2 rounded-md text-base font-medium
                            ${isActive(link.path) 
                              ? 'text-[#FFD700] bg-white/10' 
                              : 'text-white hover:text-[#FFD700]/80 hover:bg-white/5'
                            }`
                          }
                          onClick={closeMenu}
                        >
                          {getTranslatedText(link.nameKey)}
                        </Link>
                      )
                    })}
                    <div className="flex justify-center space-x-4 pt-4 border-t border-gray-700/50 mt-4">
                      {socialMediaLinks.map(social => (
                        <a
                          key={social.label}
                          href={social.href}
                          target="_blank"
                          rel="noopener noreferrer"
                          aria-label={social.label}
                          className="p-2 text-white hover:text-[#FFD700] transition-colors duration-200"
                          onClick={closeMenu}
                        >
                          {React.cloneElement(social.icon, { className: "h-6 w-6" })}
                        </a>
                      ))}
                    </div>
                  </nav>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </header>
      );
    };

    export default Navbar;
import React from 'react';
import { Building, Package, FileText, Users, DollarSign, Phone } from 'lucide-react';

export const getCategoryIcon = (category) => {
  const iconMap = {
    "UMUM": <Building className="h-5 w-5" />,
    "LAYANAN HANDLING & LAND ARRANGEMENT (LA)": <Package className="h-5 w-5" />,
    "VISA & REGULASI UMRAH": <FileText className="h-5 w-5" />,
    "UNTUK TRAVEL AGENT / MITRA": <Users className="h-5 w-5" />,
    "UNTUK JAMAAH MANDIRI / INDIVIDU": <Users className="h-5 w-5" />,
    "PAKET & HARGA": <DollarSign className="h-5 w-5" />,
    "KONTAK & PEMESANAN": <Phone className="h-5 w-5" />
  };

  return iconMap[category] || <Building className="h-5 w-5" />;
};
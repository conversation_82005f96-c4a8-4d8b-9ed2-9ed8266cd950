import React from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button.jsx';
import { Link } from 'react-router-dom';
import { MessageSquare, ArrowRight } from 'lucide-react';

const FaqFooter = ({ language }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.8, delay: 0.2, type: 'spring', stiffness: 80 }}
      className="mt-32 text-center bg-gradient-to-br from-gray-900 via-gray-800 to-background p-10 md:p-16 rounded-2xl shadow-2xl border border-gray-700/50 relative overflow-hidden"
    >
      <div className="absolute -top-16 -right-16 w-48 h-48 bg-gradient-to-br from-amber-500/10 to-transparent rounded-full blur-3xl opacity-70"></div>
      <div className="absolute -bottom-16 -left-16 w-48 h-48 bg-gradient-to-tl from-amber-500/10 to-transparent rounded-full blur-3xl opacity-70"></div>
      
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        whileInView={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.4 }}
        className="inline-block p-4 mb-6 bg-gray-700/60 rounded-full"
      >
        <MessageSquare className="w-10 h-10 text-[#FFD700]" />
      </motion.div>

      <h3 className="text-3xl md:text-4xl font-bold text-white mb-4">
        {language === 'ar' ? 'ألم تجد الإجابة التي تبحث عنها؟' :
         language === 'en' ? "Can't find the answer you're looking for?" :
         'Tidak menemukan jawaban yang Anda cari?'}
      </h3>
      <p className="text-gray-300 mb-8 text-lg max-w-2xl mx-auto leading-relaxed">
        {language === 'ar' ? 'فريقنا المتخصص على أتم الاستعداد لتقديم المساعدة. تواصل معنا للحصول على استشارة شخصية وحلول مصممة خصيصًا لتلبية احتياجاتك.' :
         language === 'en' ? 'Our dedicated team is ready to help. Reach out for a personal consultation and tailored solutions for your needs.' :
         'Tim kami yang berdedikasi siap membantu. Hubungi kami untuk konsultasi pribadi dan solusi yang disesuaikan untuk kebutuhan Anda.'}
      </p>
      <Button asChild size="lg" className="gold-gradient text-black font-semibold px-8 py-3.5 text-lg group transform hover:scale-105 transition-transform duration-300 shadow-lg shadow-amber-500/20">
        <Link to="/contact">
          {language === 'ar' ? 'اتصل بنا الآن' :
           language === 'en' ? 'Contact Us Now' :
           'Hubungi Kami Sekarang'}
          <ArrowRight className="ml-2.5 h-5 w-5 transition-transform duration-300 group-hover:translate-x-1" />
        </Link>
      </Button>
    </motion.div>
  );
};

export default FaqFooter;
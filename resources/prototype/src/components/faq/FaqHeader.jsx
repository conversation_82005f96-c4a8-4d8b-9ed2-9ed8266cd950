import React from 'react';
import { motion } from 'framer-motion';

const FaqHeader = ({ language }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: -30 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, amount: 0.5 }}
      transition={{ duration: 0.7, type: "spring", stiffness: 100 }}
      className="text-center mb-16 relative"
    >
      <div className="absolute inset-0 -z-10 bg-grid-pattern opacity-30"></div>
      <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-amber-500/10 rounded-full blur-3xl -z-10"></div>
      
      <motion.span 
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2, duration: 0.5 }}
        className="inline-block px-4 py-2 text-sm font-semibold tracking-wider text-amber-300 bg-amber-900/50 rounded-full mb-6"
      >
        {language === 'ar' ? 'مركز المساعدة' : language === 'en' ? 'Help Center' : 'Pusat Bantuan'}
      </motion.span>
      
      <h1 className="text-4xl md:text-5xl lg:text-6xl font-extrabold text-white mb-6 tracking-tight gradient-text leading-tight">
        {language === 'ar' ? 'كل ما تحتاج لمعرفته' : 
         language === 'en' ? 'Everything You Need to Know' : 
         'Semua yang Perlu Anda Ketahui'}
      </h1>
      <p className="text-lg md:text-xl text-gray-400 max-w-3xl mx-auto mb-4 leading-relaxed">
        {language === 'ar' ? 'تصفح الفئات أدناه للعثور على إجابات لأسئلتك حول خدماتنا وعملياتنا.' :
         language === 'en' ? 'Browse through the categories below to find answers to your questions about our services and processes.' :
         'Jelajahi kategori di bawah untuk menemukan jawaban atas pertanyaan Anda tentang layanan dan proses kami.'}
      </p>
    </motion.div>
  );
};

export default FaqHeader;
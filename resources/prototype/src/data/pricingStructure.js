export const pricingStructure = {
  handling_service: {
    basic: {
      "44-47": 55, "40-43": 58, "36-39": 61, "32-35": 65, "28-31": 70, "24-27": 77, "20-23": 87, "16-19": 102, "12-15": 128, "8-11": 182
    },
    premium: {
      "44-47": 75, "40-43": 78, "36-39": 81, "32-35": 85, "28-31": 90, "24-27": 97, "20-23": 107, "16-19": 122, "12-15": 148, "8-11": 203
    },
    vip: {
      "44-47": 123, "40-43": 127, "36-39": 132, "32-35": 138, "28-31": 146, "24-27": 156, "20-23": 171, "16-19": 195, "12-15": 234, "8-11": 317
    }
  },
  bundling_visa_handling: {
    basic: {
      "44-47": 196, "40-43": 201, "36-39": 205, "32-35": 211, "28-31": 218, "24-27": 231, "20-23": 246, "16-19": 266, "12-15": 298, "8-11": 357
    },
    premium: {
      "44-47": 216, "40-43": 221, "36-39": 225, "32-35": 231, "28-31": 238, "24-27": 251, "20-23": 266, "16-19": 286, "12-15": 318, "8-11": 378
    },
    vip: {
      "44-47": 264, "40-43": 270, "36-39": 276, "32-35": 284, "28-31": 294, "24-27": 311, "20-23": 330, "16-19": 359, "12-15": 404, "8-11": 492
    }
  },
  handling_airport_only: {
    prices: {
      "JED/MED – Kedatangan Saja": 19,
      "JED/MED – Kepulangan Saja": 20,
      "JED/MED – Kedatangan & Kepulangan (PP)": 33,
      "Terminal Haji – Kedatangan Saja": 16,
      "Terminal Haji – Kepulangan Saja": 24,
      "Terminal Haji – Kedatangan & Kepulangan (PP)": 35,
      "Kombinasi: Datang JED/MED, Pulang Terminal Haji": 43, // 19 + 24
      "Kombinasi: Datang Terminal Haji, Pulang JED/MED": 36  // 16 + 20
    },
    alBaik: 1
  },
  optional_addons: {
    mutawwif: {
      cost_per_day_sr: 250,
      sr_to_usd_rate: 0.266,
      sr_to_idr_rate: 3.75,
    },
    culinary: {
      al_rumansiah: 9,
    },
    thaif_tour: {
        bus_charter: 550,
        cable_car: 19,
        tobogan: 10,
        lunch: 6
    },
    haramain_train: {
        makkah_madinah: 65,
        madinah_makkah: 65,
        madinah_jed: 55,
        jed_madinah: 55
    }
  }
};

export const getPrice = (packageType, category, pax) => {
  const packagePrices = pricingStructure[packageType]?.[category];
  if (!packagePrices) return 0;

  const ranges = Object.keys(packagePrices).sort((a, b) => {
    const aMin = parseInt(a.split('-')[0], 10);
    const bMin = parseInt(b.split('-')[0], 10);
    return aMin - bMin;
  });

  for (const range of ranges) {
    const [min, max] = range.split('-').map(Number);
    if (pax >= min && pax <= max) {
      return packagePrices[range];
    }
  }
  
  return 0; 
};
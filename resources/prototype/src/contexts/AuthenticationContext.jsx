import React, { createContext, useState, useContext, useEffect, useCallback } from 'react';

const AuthenticationContext = createContext(null);

const defaultAdmin = {
  id: 'superadmin-001',
  name: 'Super Admin',
  email: '<EMAIL>',
  password: 'password123',
  role: 'Superadmin',
  lastLogin: new Date().toISOString(),
};

export const AuthenticationProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  const initializeAdminAccount = useCallback(() => {
    try {
      const storedUsers = localStorage.getItem('adminUsers');
      let users = storedUsers ? JSON.parse(storedUsers) : [];
      
      const adminExists = users.some(u => u.email === defaultAdmin.email);

      if (!adminExists) {
        users.push(defaultAdmin);
        localStorage.setItem('adminUsers', JSON.stringify(users));
      }
    } catch (error) {
      console.error('Failed to initialize admin account:', error);
    }
  }, []);

  useEffect(() => {
    setIsLoading(true);
    initializeAdminAccount();
    try {
      const storedUser = localStorage.getItem('adminUser');
      if (storedUser) {
        setUser(JSON.parse(storedUser));
      }
    } catch (error) {
      console.error("Failed to load user from localStorage:", error);
      localStorage.removeItem('adminUser');
    } finally {
      setIsLoading(false);
    }
  }, [initializeAdminAccount]);

  const login = (userData) => {
    try {
      const userToStore = { ...userData, lastLogin: new Date().toISOString() };
      localStorage.setItem('adminUser', JSON.stringify(userToStore));
      setUser(userToStore);

      const storedUsers = JSON.parse(localStorage.getItem('adminUsers') || '[]');
      const updatedUsers = storedUsers.map(u => 
        u.id === userData.id ? { ...u, lastLogin: userToStore.lastLogin } : u
      );
      localStorage.setItem('adminUsers', JSON.stringify(updatedUsers));

    } catch (error) {
      console.error("Failed to save user to localStorage:", error);
    }
  };

  const logout = () => {
    try {
      localStorage.removeItem('adminUser');
      setUser(null);
    } catch (error) {
      console.error("Failed to remove user from localStorage:", error);
    }
  };

  const value = {
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    logout,
  };

  return (
    <AuthenticationContext.Provider value={value}>
      {children}
    </AuthenticationContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthenticationContext);
  if (context === null) {
    throw new Error('useAuth must be used within an AuthenticationProvider');
  }
  return context;
};
import React, { createContext, useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import idFile from '@/locales/id.json'; // Used for initial structure if local storage is empty
import enFile from '@/locales/en.json';
import arFile from '@/locales/ar.json';

export const LanguageContext = createContext();

const getInitialTranslations = (lang) => {
  const stored = localStorage.getItem(`translations_${lang}`);
  if (stored) {
    try {
      return JSON.parse(stored);
    } catch (e) {
      console.error("Failed to parse stored translations:", e);
    }
  }
  // Fallback to imported JSON files if localStorage is empty or corrupted
  if (lang === 'id') return idFile;
  if (lang === 'en') return enFile;
  if (lang === 'ar') return arFile;
  return idFile; // Default fallback
};


export const LanguageProvider = ({ children }) => {
  const { i18n } = useTranslation();
  const [language, setLanguage] = useState(i18n.language || 'id');
  const [translations, setTranslations] = useState(getInitialTranslations(language));

  useEffect(() => {
    const handleLanguageChanged = (lng) => {
      setLanguage(lng);
      // Update translations from localStorage or fallback to JSON files
      setTranslations(getInitialTranslations(lng));
      document.documentElement.lang = lng;
      document.documentElement.dir = lng === 'ar' ? 'rtl' : 'ltr';
    };

    i18n.on('languageChanged', handleLanguageChanged);
    // Initial setup
    handleLanguageChanged(i18n.language || 'id');

    return () => {
      i18n.off('languageChanged', handleLanguageChanged);
    };
  }, [i18n]);

  const changeLanguage = (lang) => {
    if (['id', 'en', 'ar'].includes(lang)) {
      i18n.changeLanguage(lang); // This will trigger the 'languageChanged' event
    }
  };
  
  // Function to update translations in context, typically after admin saves
  const updateContextTranslations = (lang, newTranslations) => {
    if (lang === language) {
      setTranslations(newTranslations);
    }
     // Also update localStorage so it persists and is picked up on next load/language change
    localStorage.setItem(`translations_${lang}`, JSON.stringify(newTranslations));
  };


  return (
    <LanguageContext.Provider value={{ language, translations, changeLanguage, updateContextTranslations }}>
      {children}
    </LanguageContext.Provider>
  );
};
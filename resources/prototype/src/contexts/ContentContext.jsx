import React, { createContext, useState, useContext, useMemo, useEffect, useCallback } from 'react';
import { LanguageContext } from './LanguageContext';
import { partnerStorageService } from '@/services/partnerStorageService.js';

const ContentContext = createContext();

export const useContent = () => useContext(ContentContext);

const translations = {
  // Handling Packages
  featureWelcomeDrink: { id: "Welcome drink (Air Zamzam & kurma)", en: "Welcome drink (Zamzam water & dates)", ar: "مشروب ترحيبي (ماء زمزم وتمر)" },
  featureAirportHandlingFull: { id: "Handling bandara (datang & pulang)", en: "Airport handling (arrival & departure)", ar: "مناولة المطار (الوصول والمغادرة)" },
  featureMealBoxFull: { id: "Meal box bandara (datang & pulang)", en: "Airport meal box (arrival & departure)", ar: "صندوق وجبات المطار (الوصول والمغادرة)" },
  featureHotelHandlingFull: { id: "Handling hotel (check-in & check-out)", en: "Hotel handling (check-in & check-out)", ar: "مناولة الفندق (تسجيل الدخول والمغادرة)" },
  featureLuggageDistribution: { id: "Distribusi koper ke kamar", en: "Luggage distribution to rooms", ar: "توزيع الأمتعة على الغرف" },
  featureGreetingCard: { id: "Greeting card di setiap kamar", en: "Greeting card in each room", ar: "بطاقة ترحيب في كل غرفة" },
  featureSnackHemat: { id: "Snack hemat 4x trip", en: "4x budget snack trips", ar: "وجبات خفيفة اقتصادية 4 مرات" },
  featurePorterTips: { id: "Tips porter bandara", en: "Tips for airport porters", ar: "إكراميات لحمالي المطار" },
  featureBellboyTips: { id: "Tips bellboy hotel", en: "Tips for hotel bellboys", ar: "إكراميات لحمالي الفندق" },
  featureMandubTips: { id: "Tips mandub muassasah", en: "Tips for muassasah representative", ar: "إكراميات لمندوب المؤسسة" },
  featureDriverTips: { id: "Tips supir untuk semua perjalanan", en: "Tips for driver for all trips", ar: "إكراميات للسائق لجميع الرحلات" },
  featureFreeZamzam5L: { id: "Gratis Air Zamzam 5 liter (Kepulangan)", en: "Free 5 liters of Zamzam water (Departure)", ar: "5 لتر من ماء زمزم مجانًا (عند المغادرة)" },
  featureFreeDocs: { id: "Gratis dokumentasi foto/video", en: "Free photo/video documentation", ar: "توثيق مجاني بالصور/الفيديو" },
  featureMutawwifOps: { id: "Oprasional Mutawwif", en: "Mutawwif Operational", ar: "تشغيل المطوف" },
  
  featureAllBasic: { id: "Semua fasilitas paket Basic", en: "All Basic package facilities", ar: "جميع مرافق الباقة الأساسية" },
  featureWelcomeBannerOptional: { id: "X-Banner penyambutan di bus kedatangan dan hotel (Optional)", en: "Welcome X-Banner on arrival bus and hotel (Optional)", ar: "بانر ترحيبي في حافلة الوصول والفندق (اختياري)" },
  featureMealboxAlBaik: { id: "Mealbox (opsi Al Baik 1x)", en: "Mealbox (1x Al Baik option)", ar: "صندوق وجبات (خيار البيك مرة واحدة)" },
  featureSnackPremium: { id: "Snack premium 4x trip", en: "4x premium snack trips", ar: "وجبات خفيفة مميزة 4 مرات" },
  featureParcelBuah: { id: "Parcel buah di setiap kamar", en: "Fruit parcel in each room", ar: "سلة فواكه في كل غرفة" },
  featureAirMineralKamar: { id: "Air Mineral di setiap kamar", en: "Mineral water in each room", ar: "مياه معدنية في كل غرفة" },
  
  featureArabianKuliner: { id: "Arabian Kuliner di Restoran Al Romanshiah", en: "Arabian Culinary at Al Romanshiah Restaurant", ar: "مأكولات عربية في مطعم الرومانسية" },
  featureSnackVIP: { id: "Snack VIP 4x trip", en: "4x VIP snack trips", ar: "وجبات خفيفة VIP 4 مرات" },
  featureParcelBuahVIP: { id: "Parcel buah VIP di kamar", en: "VIP fruit parcel in room", ar: "سلة فواكه VIP في الغرفة" },
  featureZamzamGallon: { id: "Air Zamzam 1 galon di setiap kamar", en: "1 gallon of Zamzam water in each room", ar: "1 جالون من ماء زمزم في كل غرفة" },
  featureFreeAlBaikTrip: { id: "Gratis Al Baik (perjalanan Madinah-Makkah)", en: "Free Al Baik (Madinah-Makkah trip)", ar: "وجبة البيك مجانية (رحلة المدينة-مكة)" },

  // Bundling Package
  featureVisaUmrah: { id: "Visa Umrah", en: "Umrah Visa", ar: "تأشيرة العمرة" },
  featureBusAC: { id: "Bus AC Premium 5 trip", en: "Premium AC Bus 5 trips", ar: "حافلة مكيفة فاخرة 5 رحلات" },
  featureSaudiInsurance: { id: "Asuransi Arab Saudi", en: "Saudi Arabia Insurance", ar: "تأمين المملكة العربية السعودية" },
  featureTasrihRaudah: { id: "Tasrih Raudah", en: "Tasrih Raudah", ar: "تصريح الروضة" },
  bundlingPackageTitleShort: { id: "Bundling Handling & Visa", en: "Handling & Visa Bundle", ar: "باقة المناولة والتأشيرة" },

  excludeMutawwifRaudhah: { id: "Mutawwif & Mutawwifah Raudhah", en: "Mutawwif & Mutawwifah for Raudhah", ar: "مطوف ومطوفة للروضة" },
  noteMutawwif: { id: "Mutawwif: 250 SAR/hari", en: "Mutawwif: 250 SAR/day", ar: "مطوف: 250 ريال/يوم" },
  noteMutawwifah: { id: "Mutawwifah: 200 SAR/hari", en: "Mutawwifah: 200 SAR/day", ar: "مطوفة: 200 ريال/يوم" },
  noteJasaDorong: { id: "Jasa dorong umrah: 350 SAR", en: "Umrah wheelchair service: 350 SAR", ar: "خدمة كرسي متحرك للعمرة: 350 ريال" },
};

const handlingBasicIncludes = [
  'featureWelcomeDrink',
  'featureAirportHandlingFull',
  'featureMealBoxFull',
  'featureHotelHandlingFull',
  'featureLuggageDistribution',
  'featureGreetingCard',
  'featureSnackHemat',
  'featurePorterTips',
  'featureBellboyTips',
  'featureMandubTips',
  'featureDriverTips',
  'featureFreeZamzam5L',
  'featureFreeDocs',
  'featureMutawwifOps',
];

const handlingPremiumIncludes = [
  'featureAllBasic',
  'featureWelcomeBannerOptional',
  'featureMealboxAlBaik',
  'featureSnackPremium',
  'featureParcelBuah',
  'featureAirMineralKamar',
  'featureMutawwifOps',
];

const handlingVIPIncludes = [
  'featureAllBasic',
  'featureWelcomeBannerOptional',
  'featureArabianKuliner',
  'featureSnackVIP',
  'featureParcelBuahVIP',
  'featureZamzamGallon',
  'featureFreeAlBaikTrip',
  'featureMutawwifOps',
];

const allPackagesExcludes = ['excludeMutawwifRaudhah'];
const allPackagesNotes = ['noteMutawwif', 'noteMutawwifah', 'noteJasaDorong'];

const initialPricingPackages = [
  {
    id: 'basic',
    name: 'Basic',
    description: 'Paket handling esensial untuk kelancaran perjalanan ibadah Anda, mencakup semua kebutuhan dasar di bandara dan hotel.',
    gradientClass: 'bg-gradient-to-br from-gray-700 to-gray-900',
    textColor: 'text-slate-100',
    buttonClass: 'bg-gray-600 hover:bg-gray-500 text-white',
    popular: false,
    includes: handlingBasicIncludes,
    excludes: allPackagesExcludes,
    notes: allPackagesNotes,
    paxPricing: [
      { pax: '44-47', price: 55 },
      { pax: '40-43', price: 58 },
      { pax: '36-39', price: 61 },
      { pax: '32-35', price: 65 },
      { pax: '28-31', price: 70 },
      { pax: '24-27', price: 77 },
      { pax: '20-23', price: 87 },
      { pax: '16-19', price: 102 },
      { pax: '12-15', price: 128 },
      { pax: '8-11', price: 182 },
      { pax: '4-7', price: 366 },
    ],
  },
  {
    id: 'standard',
    name: 'Premium',
    description: 'Paket populer dengan semua layanan Basic, ditambah opsi makan Al Baik, snack premium, dan fasilitas kenyamanan lainnya.',
    gradientClass: 'bg-gradient-to-br from-amber-500 to-yellow-700',
    textColor: 'text-white',
    buttonClass: 'bg-white text-amber-800 hover:bg-amber-50',
    popularButtonClass: 'bg-gray-900 text-white hover:bg-gray-800',
    popular: true,
    includes: handlingPremiumIncludes,
    excludes: allPackagesExcludes,
    notes: allPackagesNotes,
    paxPricing: [
      { pax: '44-47', price: 75 },
      { pax: '40-43', price: 78 },
      { pax: '36-39', price: 81 },
      { pax: '32-35', price: 85 },
      { pax: '28-31', price: 90 },
      { pax: '24-27', price: 97 },
      { pax: '20-23', price: 107 },
      { pax: '16-19', price: 122 },
      { pax: '12-15', price: 148 },
      { pax: '8-11', price: 203 },
      { pax: '4-7', price: 390 },
    ],
  },
  {
    id: 'vip',
    name: 'VIP',
    description: 'Layanan premium dan eksklusif dengan semua fasilitas Basic, ditambah makan di Al Romanshiah dan fasilitas VIP lainnya.',
    gradientClass: 'bg-gradient-to-br from-gray-800 to-black',
    textColor: 'text-amber-300',
    buttonClass: 'bg-amber-400 hover:bg-amber-300 text-black',
    popular: false,
    includes: handlingVIPIncludes,
    excludes: allPackagesExcludes,
    notes: allPackagesNotes,
    paxPricing: [
      { pax: '44-47', price: 123 },
      { pax: '40-43', price: 127 },
      { pax: '36-39', price: 132 },
      { pax: '32-35', price: 138 },
      { pax: '28-31', price: 146 },
      { pax: '24-27', price: 156 },
      { pax: '20-23', price: 171 },
      { pax: '16-19', price: 195 },
      { pax: '12-15', price: 234 },
      { pax: '8-11', price: 317 },
      { pax: '4-7', price: 600 },
    ],
  },
];

const bundlingPackages = [
  {
    id: 'bundling-basic',
    name: 'Basic',
    description: 'Bundling Handling & Visa: Paket esensial untuk kelancaran perjalanan ibadah Anda.',
    gradientClass: 'bg-gradient-to-br from-gray-700 to-gray-900',
    textColor: 'text-slate-100',
    buttonClass: 'bg-gray-600 hover:bg-gray-500 text-white',
    popular: false,
    includes: [
      'featureVisaUmrah',
      'featureBusAC',
      'featureSaudiInsurance',
      'featureTasrihRaudah',
      ...handlingBasicIncludes,
    ],
    excludes: allPackagesExcludes,
    notes: allPackagesNotes,
    paxPricing: [
      { pax: '44 – 47', price: 196 },
      { pax: '40 – 43', price: 201 },
      { pax: '36 – 39', price: 205 },
      { pax: '32 – 35', price: 211 },
      { pax: '28 – 31', price: 218 },
      { pax: '24 – 27', price: 231 },
      { pax: '20 – 23', price: 246 },
      { pax: '16 – 19', price: 266 },
      { pax: '12 – 15', price: 298 },
      { pax: '8 – 11', price: 357 },
    ],
  },
  {
    id: 'bundling-premium',
    name: 'Premium',
    description: 'Bundling Handling & Visa: Paket populer dengan semua layanan Basic, ditambah fasilitas premium.',
    gradientClass: 'bg-gradient-to-br from-amber-500 to-yellow-700',
    textColor: 'text-white',
    buttonClass: 'bg-white text-amber-800 hover:bg-amber-50',
    popularButtonClass: 'bg-gray-900 text-white hover:bg-gray-800',
    popular: true,
    includes: [
      'featureVisaUmrah',
      'featureBusAC',
      'featureSaudiInsurance',
      'featureTasrihRaudah',
      ...handlingPremiumIncludes,
    ],
    excludes: allPackagesExcludes,
    notes: allPackagesNotes,
    paxPricing: [
      { pax: '44 – 47', price: 216 },
      { pax: '40 – 43', price: 221 },
      { pax: '36 – 39', price: 225 },
      { pax: '32 – 35', price: 231 },
      { pax: '28 – 31', price: 238 },
      { pax: '24 – 27', price: 251 },
      { pax: '20 – 23', price: 266 },
      { pax: '16 – 19', price: 286 },
      { pax: '12 – 15', price: 318 },
      { pax: '8 – 11', price: 378 },
    ],
  },
  {
    id: 'bundling-vip',
    name: 'VIP',
    description: 'Bundling Handling & Visa: Layanan premium dan eksklusif dengan fasilitas VIP.',
    gradientClass: 'bg-gradient-to-br from-gray-800 to-black',
    textColor: 'text-amber-300',
    buttonClass: 'bg-amber-400 hover:bg-amber-300 text-black',
    popular: false,
    includes: [
      'featureVisaUmrah',
      'featureBusAC',
      'featureSaudiInsurance',
      'featureTasrihRaudah',
      ...handlingVIPIncludes,
    ],
    excludes: allPackagesExcludes,
    notes: allPackagesNotes,
    paxPricing: [
      { pax: '44 – 47', price: 264 },
      { pax: '40 – 43', price: 270 },
      { pax: '36 – 39', price: 276 },
      { pax: '32 – 35', price: 284 },
      { pax: '28 – 31', price: 294 },
      { pax: '24 – 27', price: 311 },
      { pax: '20 – 23', price: 330 },
      { pax: '16 – 19', price: 359 },
      { pax: '12 – 15', price: 404 },
      { pax: '8 – 11', price: 492 },
    ],
  },
];

const translateContent = (content, lang, translations) => {
  if (typeof content === 'string' && translations[content]) {
    return translations[content][lang] || translations[content]['id'];
  }
  if (Array.isArray(content)) {
    return content.map(item => translateContent(item, lang, translations));
  }
  if (typeof content === 'object' && content !== null) {
    const newObj = {};
    for (const key in content) {
      if (key === 'includes' || key === 'excludes' || key === 'notes') {
        newObj[key] = content[key].map(item => {
          if (translations[item]) {
            return translations[item][lang] || translations[item]['id'];
          }
          return item;
        });
      } else {
        newObj[key] = translateContent(content[key], lang, translations);
      }
    }
    return newObj;
  }
  return content;
};

export const ContentProvider = ({ children }) => {
  const { language } = useContext(LanguageContext);
  const [content, setContent] = useState({
    partners: [],
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    setIsLoading(true);
    try {
      const partnersData = partnerStorageService.loadPartners();
      setContent({ partners: partnersData });
    } catch (error) {
      console.error("Failed to load content:", error);
      setContent({ partners: [] });
    } finally {
      setIsLoading(false);
    }
  }, []);

  const updateContent = useCallback((key, value) => {
    setContent(prevContent => {
      const newContent = { ...prevContent, [key]: value };
      try {
        if (key === 'partners') {
          partnerStorageService.savePartners(value);
        }
      } catch (error) {
        console.error(`Failed to save ${key} to localStorage:`, error);
      }
      return newContent;
    });
  }, []);

  const pricingPackages = useMemo(() => {
    return initialPricingPackages.map(pkg => translateContent(pkg, language, translations));
  }, [language]);

  const translatedBundlingPackages = useMemo(() => {
    return bundlingPackages.map(pkg => translateContent(pkg, language, translations));
  }, [language]);

  const value = {
    ...content,
    pricingPackages,
    bundlingPackages: translatedBundlingPackages,
    updateContent,
    isLoading,
  };

  return (
    <ContentContext.Provider value={value}>
      {children}
    </ContentContext.Provider>
  );
};